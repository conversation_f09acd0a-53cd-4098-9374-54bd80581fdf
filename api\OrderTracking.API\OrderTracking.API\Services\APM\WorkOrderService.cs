﻿using APMWebDataInterface.ExampleDataModel;
using APMWebDataInterface.Generated.section510_Asset_Walkdown_Details_F;
using APMWebDataInterface.Generated.section570_Asset_Walkdown_Details_F;
using APMWebDataInterface.Generated.section653_Asset_Walkdown_Details_F;
using APMWebDataInterface.Headless.Entities;
using CommonDataInterface;
using CommonDataInterface.Attributes;
using MoreLinq.Extensions;
using OrderTracking.API.Extensions.APM;
using OrderTracking.API.Models.APM;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace OrderTracking.API.Services.APM
{
    public class WorkOrderService : IWorkOrderService
    {
        private readonly APM_WebDataInterface _apm;
        private readonly IProjectService _projectService;

        public WorkOrderService(APM_WebDataInterface apm, IProjectService projects)
        {
            _apm = apm;
            _projectService = projects;
        }

        /// <summary>
        ///     Get all work orders
        /// </summary>
        /// <returns></returns>
        /// <param name="email"></param>
        public async Task<WorkOrder[]> Get(string email)
        {
            try
            {
                var projects = _projectService.Get(email);
                var projectIds = projects.Select(p => p.id).ToArray();
                var workOrders = await _apm.GetWorkOrders(projectIds, email);
                return workOrders;
            }
            catch (Exception ex) when (ex.Message.Contains("Inspection not found") || ex.Message.Contains("CMS"))
            {
                // Handle CMS inspection service unavailability gracefully
                // Return empty array to maintain service functionality
                Console.WriteLine($"CMS Inspection service unavailable, returning empty work orders for user {email}: {ex.Message}");
                return new WorkOrder[0];
            }
        }

        /// <summary>
        ///     Get work orders associated with one or more projects (via project id)
        /// </summary>
        /// <param name="email"></param>
        /// <param name="projectIds"></param>
        /// <returns></returns>
        public async Task<WorkOrder[]> GetByProject(string email, params string[] projectIds)
        {
            try
            {
                var workOrders = await _apm.GetWorkOrders(projectIds, email);
                return workOrders;
            }
            catch (Exception ex) when (ex.Message.Contains("Inspection not found") || ex.Message.Contains("CMS"))
            {
                // Handle CMS inspection service unavailability gracefully
                // Return empty array to maintain service functionality
                Console.WriteLine($"CMS Inspection service unavailable, returning empty work orders for user {email}: {ex.Message}");
                return new WorkOrder[0];
            }
        }

        /// <summary>
        ///     Get work order by id (takes longer than if you can provide a project id).  If
        ///     you have the project id, please use <see cref="DevExpress.CodeParser.Get" /> with an id and projectId parameter.
        /// </summary>
        /// <param name="id"></param>
        /// <param name="email"></param>
        /// <returns></returns>
        public async Task<WorkOrder> Get(string id, string email)
        {
            var projects = _projectService.Get(email);
            var projectIds = projects.Select(p => p.id);
            var workOrders = await GetByProject(email, projectIds.ToArray());
            var workOrder = workOrders.FirstOrDefault(w => w.id == id);
            return workOrder;
        }

        /// <summary>
        ///     Get Work Orders from the same Asset by their ids
        /// </summary>
        /// <param name="asset"></param>
        /// <param name="workOrderIds"></param>
        /// <param name="email"></param>
        /// <returns></returns>
        public async Task<IEnumerable<WorkOrder>> GetByAsset(Asset asset, IEnumerable<string> workOrderIds, string email)
        {
            try
            {
                var workOrders = await _apm.GetWorkOrders(asset, workOrderIds, email);
                return workOrders;
            }
            catch (Exception ex) when (ex.Message.Contains("Inspection not found") || ex.Message.Contains("CMS"))
            {
                // Handle CMS inspection service unavailability gracefully
                // Return empty collection to maintain service functionality
                Console.WriteLine($"CMS Inspection service unavailable, returning empty work orders for asset {asset?.id} and user {email}: {ex.Message}");
                return new List<WorkOrder>();
            }
        }

        /// <summary>
        ///     Get Work Order by id
        /// </summary>
        /// <param name="id"></param>
        /// <param name="projectId"></param>
        /// <param name="email"></param>
        /// <returns></returns>
        public async Task<WorkOrder> Get(string id, string projectId, string email)
        {
            try
            {
                var workOrders = await _apm.GetWorkOrders(new[] { projectId }, email);
                var workOrder = workOrders.FirstOrDefault(w => w.id == id);
                return workOrder;
            }
            catch (Exception ex) when (ex.Message.Contains("Inspection not found") || ex.Message.Contains("CMS"))
            {
                // Handle CMS inspection service unavailability gracefully
                // Return null to indicate work order not found
                Console.WriteLine($"CMS Inspection service unavailable, cannot retrieve work order {id} for user {email}: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        ///     Creates a new work order associated with a given asset and project from a <see cref="NewWorkOrder" />
        /// </summary>
        /// <param name="newWorkOrder"></param>
        /// <param name="asset"></param>
        /// <param name="project"></param>
        /// <param name="email"></param>
        /// <param name="businessUnitId"></param>
        /// <returns></returns>
        public async Task<WorkOrder> Create(NewWorkOrder newWorkOrder, Asset asset, Project project, string email, string businessUnitId)
        {
            var workOrder = WorkOrderExtensions.Create(asset, project, newWorkOrder);
            workOrder.businessUnitId.SetValue(businessUnitId);
            await workOrder.SavePendingChanges(email);
            return workOrder;
        }

        /// <summary>
        ///     Creates a task on a work order from a <see cref="NewTask" />
        /// </summary>
        /// <param name="workOrder"></param>
        /// <param name="newTask"></param>
        /// <param name="email"></param>
        /// <param name="businessUnitId"></param>
        /// <returns></returns>
        public async Task<APMTask> CreateTask(WorkOrder workOrder, NewTask newTask, string email, string businessUnitId)
        {
            var task = workOrder.AddNewTask(newTask);
            task.businessUnitId.SetValue(businessUnitId);
            await workOrder.SavePendingChanges(email);
            return task;
        }

        /// <summary>
        ///     Update a work order and relevant tasks in the work order from a <see cref="FiveTenAssetDetailsUpdate" />
        /// </summary>
        /// <param name="workOrder"></param>
        /// <param name="assetDetails"></param>
        /// <param name="email"></param>
        /// <returns></returns>
        public async Task Update(WorkOrder workOrder, FiveTenAssetDetailsUpdate assetDetails, string email)
        {
            var walkDown = workOrder.asset.walkDown as Section510_Asset_Walkdown_Details_F;

            walkDown.Update(assetDetails);

            await workOrder.SavePendingChanges(email);
            await workOrder.asset.SavePendingChanges(email);
        }

        /// <summary>
        ///     Update a work order and relevant tasks in the work order from a <see cref="FiveSeventyWalkdown" />
        /// </summary>
        /// <param name="workOrder"></param>
        /// <param name="assetDetails"></param>
        /// <param name="email"></param>
        /// <returns></returns>
        public async Task Update(WorkOrder workOrder, FiveSeventyWalkdown assetDetails, string email)
        {
            var details = workOrder.asset.walkDown as Section570_Asset_Walkdown_Details_F;

            details.Update(assetDetails);

            await workOrder.SavePendingChanges(email);
            await workOrder.asset.SavePendingChanges(email);
        }

        /// <summary>
        ///     Update a work order and relevant tasks in the work order from a <see cref="SixFiftyThreeWalkDown" />
        /// </summary>
        /// <param name="workOrder"></param>
        /// <param name="assetDetails"></param>
        /// <param name="email"></param>
        /// <returns></returns>
        public async Task Update(WorkOrder workOrder, SixFiftyThreeWalkDown assetDetails, string email)
        {
            var details = workOrder.asset.walkDown as Section653_Asset_Walkdown_Details_F;

            details.Update(assetDetails);

            await workOrder.SavePendingChanges(email);
            await workOrder.asset.SavePendingChanges(email);
        }

        /// <summary>
        ///     Deletes a Visual Inspection Photo using a <see cref="VisualInspectionPhotoTransport" />
        /// </summary>
        /// <param name="visualInspectionPhoto"></param>
        /// <param name="email"></param>
        /// <returns></returns>
        public async Task DeleteVisualInspectionPhoto(VisualInspectionPhotoTransport visualInspectionPhoto,
            string email)
        {
            var (task, question, photo) = await FindPhotoAsync(visualInspectionPhoto, email);

            question.RemovePhoto(photo);
            await task.SavePendingChanges(email);
        }

        /// <summary>
        ///     Updates the description of a Visual Inspection Photo using a <see cref="VisualInspectionPhotoTransport" />
        /// </summary>
        /// <param name="visualInspectionPhoto"></param>
        /// <param name="email"></param>
        /// <returns></returns>
        public async Task UpdateVisualInspectionPhotoDescription(VisualInspectionPhotoTransport visualInspectionPhoto,
            string email)
        {
            var (task, _, photo) = await FindPhotoAsync(visualInspectionPhoto, email);

            photo.Description.SetValue(visualInspectionPhoto.Description);

            await task.SavePendingChanges(email);
        }

        /// <summary>
        ///     Updates the photo description of a photo in the asset details section of a work order (current walk down)
        /// </summary>
        /// <param name="photo"></param>
        /// <param name="email"></param>
        /// <returns></returns>
        public async Task UpdateAssetDetailsPhotoDescription(AssetDetailsPhotoTransport photo, string email)
        {
            async Task UpdateDescription(MediaEntry p, WorkOrder workOrder)
            {
                if (p == null) throw new AssetDetailsPhotoNotFoundException(photo);

                p.Description.SetValue(photo.Description);

                await workOrder.SavePendingChanges(email);
                await workOrder.asset.SavePendingChanges(email);
            }

            switch (photo.AssetType)
            {
                case "653":
                    {
                        var (workOrder, _, p) = await FindAssetSixFiftyThreeDetailsPhoto(photo, email);
                        await UpdateDescription(p, workOrder);
                        break;
                    }
                case "510":
                    {
                        var (workOrder, _, p) = await FindAssetFiveTenDetailsPhoto(photo, email);
                        await UpdateDescription(p, workOrder);
                        break;
                    }
                case "570":
                    {
                        var (workOrder, _, p) = await FindAssetFiveSeventyDetailsPhoto(photo, email);
                        await UpdateDescription(p, workOrder);
                        break;
                    }
            }
        }

        /// <summary>
        ///     Deletes a photo in the asset details section of a work order (current walk down)
        /// </summary>
        /// <param name="photo"></param>
        /// <param name="email"></param>
        /// <returns></returns>
        public async Task DeleteAssetDetailsPhoto(AssetDetailsPhotoTransport photo, string email)
        {
            async Task SaveChanges(WorkOrder workOrder)
            {
                await workOrder.asset.SavePendingChanges(email);
                await workOrder.SavePendingChanges(email);
            }

            switch (photo.AssetType)
            {
                case "653":
                    {
                        var (workOrder, details, p) = await FindAssetSixFiftyThreeDetailsPhoto(photo, email);

                        if (p == null) throw new AssetDetailsPhotoNotFoundException(photo);

                        switch (photo.Section)
                        {
                            case "Front":
                                details.sectionGeneralInformation.sectionPhotos.attributeFront.RemovePhoto(p);
                                break;
                            case "Back":
                                details.sectionGeneralInformation.sectionPhotos.attributeBack.RemovePhoto(p);
                                break;
                            case "Left":
                                details.sectionGeneralInformation.sectionPhotos.attributeLeft.RemovePhoto(p);
                                break;
                            case "Right":
                                details.sectionGeneralInformation.sectionPhotos.attributeRight.RemovePhoto(p);
                                break;
                        }

                        await SaveChanges(workOrder);
                        break;
                    }
                case "510":
                    {
                        var (workOrder, details, p) = await FindAssetFiveTenDetailsPhoto(photo, email);

                        if (p == null) throw new AssetDetailsPhotoNotFoundException(photo);

                        switch (photo.Section)
                        {
                            case "Front":
                                details.sectionGeneralInformation.sectionPhotos.attributeFront.RemovePhoto(p);
                                break;
                            case "Back":
                                details.sectionGeneralInformation.sectionPhotos.attributeBack.RemovePhoto(p);
                                break;
                            case "Left":
                                details.sectionGeneralInformation.sectionPhotos.attributeLeft.RemovePhoto(p);
                                break;
                            case "Right":
                                details.sectionGeneralInformation.sectionPhotos.attributeRight.RemovePhoto(p);
                                break;
                        }

                        await SaveChanges(workOrder);
                        break;
                    }
                case "570":
                    {
                        var (workOrder, details, p) = await FindAssetFiveSeventyDetailsPhoto(photo, email);

                        if (p == null) throw new AssetDetailsPhotoNotFoundException(photo);

                        switch (photo.Section)
                        {
                            case "Front":
                                details.sectionGeneralInformation.sectionPhotos.attributeFront.RemovePhoto(p);
                                break;
                            case "Back":
                                details.sectionGeneralInformation.sectionPhotos.attributeBack.RemovePhoto(p);
                                break;
                            case "Left":
                                details.sectionGeneralInformation.sectionPhotos.attributeLeft.RemovePhoto(p);
                                break;
                            case "Right":
                                details.sectionGeneralInformation.sectionPhotos.attributeRight.RemovePhoto(p);
                                break;
                        }

                        await SaveChanges(workOrder);
                        break;
                    }
            }
        }

        /// <summary>
        ///     Update the published state of a work order
        /// </summary>
        /// <param name="workOrder"></param>
        /// <param name="update"></param>
        /// <param name="email"></param>
        /// <returns></returns>
        public async Task<WorkOrder> UpdatePublishedState(WorkOrder workOrder, PublishUpdateObject update, string email)
        {
            if (update.IsPublishing)
                workOrder.MarkThisAndTasksAsPublished();
            else
                workOrder.UnMarkThisAndTasksAsPublished();
            await workOrder.SavePendingChanges(email);
            return workOrder;
        }

        /// <summary>
        ///     Update a work order from a <see cref="WorkOrderTransportObject" />
        /// </summary>
        /// <param name="workOrder"></param>
        /// <param name="update"></param>
        /// <param name="email"></param>
        /// <returns></returns>
        public async Task Update(WorkOrder workOrder, WorkOrderTransportObject update, string email)
        {
            workOrder.Update(update);
            await workOrder.SavePendingChanges(email);
        }

        /// <summary>
        ///     Update a work order from a <see cref="VisualInspection" />
        /// </summary>
        /// <param name="workOrder"></param>
        /// <param name="inspection"></param>
        /// <param name="email"></param>
        /// <returns></returns>
        public async Task Update(WorkOrder workOrder, VisualInspection inspection, string email)
        {
            workOrder.Update(inspection);
            await workOrder.SavePendingChanges(email);
        }

        /// <summary>
        ///     Updates a work orders task from a <see cref="TaskUpdate" />
        /// </summary>
        /// <param name="workOrder"></param>
        /// <param name="taskId"></param>
        /// <param name="taskUpdate"></param>
        /// <param name="email"></param>
        /// <returns></returns>
        public async Task<APMTask> UpdateTask(WorkOrder workOrder, string taskId, TaskUpdate taskUpdate, string email)
        {
            var task = workOrder.tasks.FirstOrDefault(t => t.id == taskId);

            if (task == null) throw new TasksNotFoundException(taskId);

            task.Update(taskUpdate);

            await task.SavePendingChanges(email);

            if (taskUpdate.Status != null && taskUpdate.Status != task.status.CurrentValue)
            {
                var status = task.StatusFromString(taskUpdate.Status);
                task.ChangeStatus(status);
                await task.SavePendingChanges(email);
            }

            await workOrder.SavePendingChanges(email);

            return task;
        }

        private async Task<(APMTask, AttributeBase, MediaEntry)> FindPhotoAsync(
            VisualInspectionPhotoTransport visualInspectionPhoto, string email)
        {

            var workOrder = await Get(visualInspectionPhoto.WorkOrderId, visualInspectionPhoto.ProjectId, email);
            if (workOrder == null) return (null, null, null);

            var task = workOrder.tasks.FirstOrDefault(task => task.id == visualInspectionPhoto.TaskDatabaseId);
            if (task == null) return (null, null, null);

            var sections = task.taskType switch
            {
                "Full" => GetSections(task),
                _ => task.getTaskData().GetChildren().ToArray()
            };

            var section = sections?.FirstOrDefault(section =>
            {
                if (section is Category GenericSection)
                {
                    return string.Equals(GenericSection.DatabaseName, visualInspectionPhoto.SectionDatabaseName, StringComparison.CurrentCultureIgnoreCase);
                }
                else
                {
                    return string.Equals(section.GetType().Name, visualInspectionPhoto.SectionDatabaseName, StringComparison.CurrentCultureIgnoreCase);
                }
            });

            if (section == null) return (task, null, null);

            var questions = section.GetChildren().OfType<AttributeBase>();
            var question = questions.FirstOrDefault(question =>
                question.DatabaseName == visualInspectionPhoto.QuestionDatabaseName);

            if (question == null)
            {
                //Check subsections
                var subItems = section.GetChildren().OfType<Category>();
                foreach (var sub in subItems)
                {
                    questions = sub.GetChildren().OfType<AttributeBase>();
                    question = questions.FirstOrDefault(question =>
                        question.DatabaseName == visualInspectionPhoto.QuestionDatabaseName);

                    if (question != null) break;
                }
            }

            var photo = question?.Photos.FirstOrDefault(photo =>
                photo.DatabaseId.StartsWith(visualInspectionPhoto.PhotoDatabaseId.Split(".").FirstOrDefault()));
            return (task, question, photo);
        }

        private DataModelItem[] GetSections(APMTask task)
        {
            if (task.getTaskData() is FullInspection fullInspection)
            {
                return fullInspection?.GetChildren()
                .SelectMany(s => s.GetChildren())
                .ToArray();
            }

            return null;
        }

        private async Task<(WorkOrder workOrder, Section653_Asset_Walkdown_Details_F details, MediaEntry p)> FindAssetSixFiftyThreeDetailsPhoto(AssetDetailsPhotoTransport photo, string email)
        {
            var workOrder = await Get(photo.WorkOrderId, photo.ProjectId, email);

            var details = workOrder?.asset.walkDown as Section653_Asset_Walkdown_Details_F;

            var p = photo.Section switch
            {
                "Front" => details?.sectionGeneralInformation.sectionPhotos.attributeFront.Photos.FirstOrDefault(
                    frontPhoto => frontPhoto.DatabaseId == photo.PhotoDatabaseId),
                "Back" => details?.sectionGeneralInformation.sectionPhotos.attributeBack.Photos.FirstOrDefault(
                    backPhoto => backPhoto.DatabaseId == photo.PhotoDatabaseId),
                "Left" => details?.sectionGeneralInformation.sectionPhotos.attributeLeft.Photos.FirstOrDefault(
                    leftPhoto => leftPhoto.DatabaseId == photo.PhotoDatabaseId),
                "Right" => details?.sectionGeneralInformation.sectionPhotos.attributeRight.Photos.FirstOrDefault(
                    rightPhoto => rightPhoto.DatabaseId == photo.PhotoDatabaseId),
                _ => null
            };

            return (workOrder, details, p);
        }

        private async Task<(WorkOrder workOrder, Section510_Asset_Walkdown_Details_F details, MediaEntry p)> FindAssetFiveTenDetailsPhoto(AssetDetailsPhotoTransport photo, string email)
        {
            var workOrder = await Get(photo.WorkOrderId, photo.ProjectId, email);

            var details = workOrder?.asset.walkDown as Section510_Asset_Walkdown_Details_F;

            var p = photo.Section switch
            {
                "Front" => details?.sectionGeneralInformation.sectionPhotos.attributeFront.Photos.FirstOrDefault(
                    frontPhoto => frontPhoto.DatabaseId == photo.PhotoDatabaseId),
                "Back" => details?.sectionGeneralInformation.sectionPhotos.attributeBack.Photos.FirstOrDefault(
                    backPhoto => backPhoto.DatabaseId == photo.PhotoDatabaseId),
                "Left" => details?.sectionGeneralInformation.sectionPhotos.attributeLeft.Photos.FirstOrDefault(
                    leftPhoto => leftPhoto.DatabaseId == photo.PhotoDatabaseId),
                "Right" => details?.sectionGeneralInformation.sectionPhotos.attributeRight.Photos.FirstOrDefault(
                    rightPhoto => rightPhoto.DatabaseId == photo.PhotoDatabaseId),
                _ => null
            };

            return (workOrder, details, p);
        }

        private async Task<(WorkOrder workOrder, Section570_Asset_Walkdown_Details_F details, MediaEntry? p)> FindAssetFiveSeventyDetailsPhoto(AssetDetailsPhotoTransport photo, string email)
        {
            var workOrder = await Get(photo.WorkOrderId, photo.ProjectId, email);

            var details = workOrder?.asset.walkDown as Section570_Asset_Walkdown_Details_F;

            var p = photo.Section switch
            {
                "Front" => details?.sectionGeneralInformation.sectionPhotos.attributeFront.Photos.FirstOrDefault(
                    frontPhoto => frontPhoto.DatabaseId == photo.PhotoDatabaseId),
                "Back" => details?.sectionGeneralInformation.sectionPhotos.attributeBack.Photos.FirstOrDefault(
                    backPhoto => backPhoto.DatabaseId == photo.PhotoDatabaseId),
                "Left" => details?.sectionGeneralInformation.sectionPhotos.attributeLeft.Photos.FirstOrDefault(
                    leftPhoto => leftPhoto.DatabaseId == photo.PhotoDatabaseId),
                "Right" => details?.sectionGeneralInformation.sectionPhotos.attributeRight.Photos.FirstOrDefault(
                    rightPhoto => rightPhoto.DatabaseId == photo.PhotoDatabaseId),
                _ => null
            };

            return (workOrder, details, p);
        }

        public WorkOrderVM BuildWorkOrderVM(WorkOrder workOrder)
        {

            var walkDown = workOrder.asset.walkDown;
            // Determine the equipment ID based on the walk down type (510, 570, 653)
            var assetId = walkDown switch
            {
                Section510_Asset_Walkdown_Details_F fiveTenWalkDown => fiveTenWalkDown
                    .sectionIdentification
                    .attributeNumber_or_ID
                    .CurrentValue,
                Section570_Asset_Walkdown_Details_F fiveSeventyWalkDown => fiveSeventyWalkDown
                    .sectionIdentification
                    .attributeNumber_or_Circuit_ID
                    .CurrentValue,
                Section653_Asset_Walkdown_Details_F sixFiftyThreeWalkDown => sixFiftyThreeWalkDown
                    .sectionIdentification
                    .attributeNumber_or_ID
                    .CurrentValue,
                _ => null
            };
            return new WorkOrderVM
            {
                ID = workOrder.id,
                ProjectId = workOrder.projectId,
                AssetId = assetId,
                ApmWorkOrderNumber = workOrder.apmWorkOrderNumber.CurrentValue,
                Status = workOrder.status.CurrentValue,
                FacilityName = workOrder.facilityName.CurrentValue,
                AssetCategory = workOrder.asset.assetCategory,
                PlannedStart = workOrder.plannedStart.CurrentValueDateTime,
                PlannedEnd = workOrder.plannedEnd.CurrentValueDateTime,
                DueDate = workOrder.dueDate.CurrentValueDateTime,
                Asset = workOrder.asset,
                BusinessUnitId = workOrder.businessUnitId.CurrentValue
            };

        }
    }

    /// <summary>
    ///     Exception to throw when a photo is not found on the asset details (current walk down) of a work order
    /// </summary>
    public class AssetDetailsPhotoNotFoundException : Exception
    {
        /// <summary>
        ///     Constructor...
        /// </summary>
        /// <param name="photo"></param>
        public AssetDetailsPhotoNotFoundException(AssetDetailsPhotoTransport photo) : base(
            $"Photo was not found (id: {photo.PhotoDatabaseId}, project id: {photo.ProjectId}, work order id: {photo.WorkOrderId}, asset type: {photo.AssetType}, section: {photo.Section}")
        {
            PhotoDatabaseId = photo.PhotoDatabaseId;
            ProjectId = photo.ProjectId;
            WorkOrderId = photo.WorkOrderId;
            AssetType = photo.AssetType;
            Section = photo.Section;
        }

        public string PhotoDatabaseId { get; set; }

        public string ProjectId { get; set; }

        public string WorkOrderId { get; set; }

        public string AssetType { get; set; }

        public string Section { get; set; }
    }
}