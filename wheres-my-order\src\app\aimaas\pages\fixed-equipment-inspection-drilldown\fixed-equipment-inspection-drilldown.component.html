<div *ngIf="canShowInspectionList">
    <app-breadcrumbs *ngIf="!hideBreadcrumbs"
                     [crumbs]="crumbs"> </app-breadcrumbs>
    <!-- Inspection details popup -->
    <dx-popup [(visible)]="popupVisible"
              [width]="1200"
              [height]="700"
              [showTitle]="true"
              [title]="inspectionPopupTitle"
              [dragEnabled]="false"
              [showCloseButton]="true"
              (onHiding)="closePopup()">

        <div class="tabs-demo">
            <div class="widget-container">
                <dx-tab-panel>
                    <dxi-item title="LAST INSPECTION DETAILS">
                        <div *dxTemplate="let data">
                            <dx-scroll-view [height]=600>
                                <app-general-inspection-details
                                                                [generalinspection]="generalInspectionDetails">
                                </app-general-inspection-details>
                            </dx-scroll-view>
                        </div>
                    </dxi-item>
                    <dxi-item title="Inspection Anomalies">
                        <div *dxTemplate="let data">
                            <dx-scroll-view [height]=600>

                                <app-inspection-anomalies
                                                          [inspectionanomalies]="visibleInspectionAnamolies">
                                </app-inspection-anomalies>
                            </dx-scroll-view>
                        </div>
                    </dxi-item>
                    <dxi-item title="UT Readings">
                        <div *dxTemplate="let data">
                            <dx-scroll-view [height]=600>
                                <ng-container *ngIf="popupVisible">
                                    <app-corrosion-analysis
                                                            [operationId]="selectedOperationId">
                                    </app-corrosion-analysis>
                                </ng-container>

                            </dx-scroll-view>
                        </div>
                    </dxi-item>
                    <dxi-item title="Inspection Attachments">
                        <div *dxTemplate="let data">
                            <dx-scroll-view [height]=600>
                                <ng-container *ngIf="popupVisible">
                                    <app-inspection-attachments
                                                                [operationId]="selectedOperationId">
                                    </app-inspection-attachments>
                                </ng-container>
                            </dx-scroll-view>
                        </div>
                    </dxi-item>


                </dx-tab-panel>

            </div>
        </div>
    </dx-popup>
    <!-- Asset details popup -->

    <dx-popup [(visible)]="assetDetailsPopupVisible"
              [width]="1200"
              [height]="700"
              [showTitle]="true"
              [title]="equipmentPopupTitle"
              [dragEnabled]="false"
              [showCloseButton]="true"
              (onHiding)="closePopup()">

        <div class="tabs-demo">
            <div class="widget-container">
                <dx-tab-panel>

                    <dxi-item title="General Information">
                        <div *dxTemplate="let data">
                            <dx-scroll-view [height]=550>
                                <ng-container *ngIf="assetDetailsPopupVisible">


                                    <app-asset-generalinformation [selectedAssetId]="currentAssetId"
                                                                  [asset]="currentAssetDetails">
                                    </app-asset-generalinformation>
                                </ng-container>
                            </dx-scroll-view>
                        </div>
                    </dxi-item>
                    <dxi-item title="Inspection Schedule">
                        <div *dxTemplate="let data">
                            <dx-scroll-view [height]=550>
                                <ng-container *ngIf="assetDetailsPopupVisible">
                                    <app-inspection-schedule [assetinspection]="inspectionSchedule"
                                                             (CompleteHistoryButtonClicked)="CompleteHistoryButtonClicked($event)">
                                    </app-inspection-schedule>
                                </ng-container>
                            </dx-scroll-view>
                        </div>

                    </dxi-item>

                    <dxi-item title="Component Details">
                        <div *dxTemplate="let data">
                            <dx-scroll-view [height]=540>
                                <app-asset-component-details
                                                             [assetComponent]="assetComponentMap">
                                </app-asset-component-details>
                            </dx-scroll-view>
                        </div>
                    </dxi-item>
                    <dxi-item title="General Analysis">
                        <div *dxTemplate="let data">
                            <dx-scroll-view [height]=600>
                                <ng-container *ngIf="assetDetailsPopupVisible">
                                    <app-general-analysis
                                                          [assetId]="currentAssetId">
                                    </app-general-analysis>
                                </ng-container>
                            </dx-scroll-view>
                        </div>
                    </dxi-item>
                    <dxi-item title="Asset Attachments">
                        <div *dxTemplate="let data">
                            <dx-scroll-view [height]=600>
                                <ng-container *ngIf="assetDetailsPopupVisible">
                                    <app-attachments
                                                     [selectedAssetId]="currentAssetId">
                                    </app-attachments>
                                </ng-container>
                            </dx-scroll-view>
                        </div>
                    </dxi-item>
                    <dxi-item title="Submissions">


                        <div *dxTemplate="let data">
                            <ng-container *ngIf="assetDetailsPopupVisible">
                                <dx-scroll-view [height]=600>


                                    <app-submissions
                                                     [selectedAssetId]="currentAssetId">
                                    </app-submissions>
                                </dx-scroll-view>
                            </ng-container>
                        </div>
                    </dxi-item>
                </dx-tab-panel>

            </div>
        </div>
    </dx-popup>

    <dx-popup [(visible)]="submissionPopupVisible"
              [width]="1200"
              [height]="700"
              [showTitle]="true"
              [dragEnabled]="false"
              [showCloseButton]="true">
        <dxi-toolbar-item toolbar="top"
                          [text]="submissionPopupTitle"
                          location="center"></dxi-toolbar-item>

        <ng-container *ngIf="submissionPopupVisible">

            <app-client-data-submission [initialAnomaly]="initialAnomaly"
                                        (clientSubmissionTitleValueChange)="clientSubmissionTitleValueChange($event)"
                                        (formSubmittedValueChange)="clientDataFormSubmitted($event)">
            </app-client-data-submission>
        </ng-container>
    </dx-popup>

    <div class="dx-card content-block responsive-paddings"
         style="max-width: 94vw;">
        <div
             style="display: flex; justify-content: space-between; align-items: center;">
            <h3>Inspections and Schedules{{ isFromOverview ? ' - ' +
                breaCrumbLabel : '' }}</h3>
            <div style="margin-left: auto;"
                 *ngIf="!isHideButton">

                <dx-button [routerLink]="['../drilldown']"
                           class="listpagebuttons">
                    Equipment List
                </dx-button>
                <!-- <dx-button [routerLink]="['../inspection-drilldown']"
             class="listpagebuttons">
      Inspections List
  </dx-button> -->
                <dx-button [routerLink]="['../anomaly-drilldown']"
                           class="listpagebuttons">
                    Recommendations List
                </dx-button>


                <dx-button *ngIf="availableSites?.length > 0 && (
                                    (currentUser | hasRole: 'AIMaaS:Edit') ||
                                    (currentUser | hasRole: 'App:Admin') ||
                                    (currentUser | hasRole: 'AIMaaS:Admin') ||
                                    (currentUser | hasRole: 'AIMaaS:Demo') ||
                                    (currentUser | hasRole: 'AIMaaS:All'))"
                           (onClick)="clientSubmitDataOnclick('frombuttonclick')"
                           class="listpagebuttons">
                    Action Center
                </dx-button>
            </div>
            <div>
                <dx-button *ngIf="isHideButton"
                           (onClick)="onCloseTab()"
                           class="listpagebuttons"> Close tab
                </dx-button>
            </div>
        </div>
        <div class="top-row">
            <dx-select-box id="selectBox4"
                           #districtSelectionBox
                           *ngIf="availableSites && !isFromOverview"
                           [(value)]="selectedCostCentres"
                           [items]="filteredDistrictOptions"
                           displayExpr="name"
                           valueExpr="id"
                           placeholder="Select District"
                           [showSelectionControls]="false"
                           [searchEnabled]="true"
                           style="width:370px; margin:2px"
                           (onValueChanged)="onDistrictChange($event)">
                <dxo-drop-down-options
                                       container="#selectBox4"></dxo-drop-down-options>
            </dx-select-box>

            <dx-select-box id="selectBox3"
                           #oisClientSelectionBox
                           *ngIf="availableSites && !isFromOverview"
                           [(value)]="selectedCompany"
                           [items]="filteredClientOptions"
                           displayExpr="name"
                           valueExpr="id"
                           placeholder="Select Company"
                           [showSelectionControls]="false"
                           [searchEnabled]="true"
                           style="width:370px; margin:2px"
                           (onValueChanged)="onClientChange($event)">
                <dxo-drop-down-options
                                       container="#selectBox3"></dxo-drop-down-options>
            </dx-select-box>
        </div>
        <dx-select-box #siteSelectionBox
                       id="selectBox"
                       *ngIf="availableSites"
                       style="width:370px; margin:2px"
                       [items]="availableSites"
                       [displayExpr]="customDisplayExpr"
                       [(value)]="selectedSite"
                       [hint]="selectedSite | siteLabel"
                       [showClearButton]="false"
                       [searchEnabled]="true"
                       itemTemplate="item"
                       stylingMode="filled"
                       (onSelectionChanged)="changeSite($event)">
            <dxo-drop-down-options container="#selectBox"
                                   [closeOnTargetScroll]="false"></dxo-drop-down-options>
            <div *dxTemplate="let data of 'item'">
                <div style="display:inline-block">{{data | siteLabel}} </div>

            </div>
        </dx-select-box>


        <dx-data-grid #grid
                      id="gridload"
                      class="gridload"
                      [dataSource]="inspectionDataSource"
                      [showBorders]="true"
                      [allowColumnResizing]="true"
                      [selectedRowKeys]="[]"
                      [allowColumnReordering]="true"
                      [filterValue]="currentFilter"
                      [filterSyncEnabled]="true"
                      (onContentReady)="onContentReady($event)"
                      (onCellPrepared)="onCellPrepared($event)"
                      (onRowExpanding)="onRowExpanding($event)"
                      (onRowClick)="inspectionSelectionChanged($event)"
                      (onExporting)="onExporting($event)"
                      [wordWrapEnabled]="true">

            <dxo-load-panel [enabled]="true"></dxo-load-panel>
            <!-- [masterDetail]="{ enabled: true, template: 'detail' }" -->
            <!-- Paging -->
            <dxo-paging [enabled]="true"
                        [pageSize]="10"></dxo-paging>
            <dxo-pager [showPageSizeSelector]="true"
                       [allowedPageSizes]="[5, 10, 25, 50]"></dxo-pager>

            <!-- Export -->
            <dxo-export [enabled]="true"></dxo-export>
            <dxo-toolbar>
                <dxi-item name="groupPanel"></dxi-item>
                <dxi-item widget="dxButton"
                          location="after"
                          [options]="{icon: 'fa fa-undo', hint: 'Restore Grid Defaults', onClick: restoreAssetsDefaultsClicked}">
                </dxi-item>
                <dxi-item name="columnChooserButton"></dxi-item>
                <dxi-item name="exportButton"></dxi-item>
            </dxo-toolbar>
            <dxo-state-storing [enabled]="true"
                               type="custom"
                               storageKey="inspection"
                               [customLoad]="loadState"
                               [customSave]="saveState">
            </dxo-state-storing>
            <dxo-column-chooser [enabled]="true"
                                mode="dragAndDrop"
                                [width]="310"
                                title="Column Chooser (Drag and Drop)">
            </dxo-column-chooser>
            <dxo-filter-row [visible]="true"></dxo-filter-row>
            <dxo-header-filter [visible]="true"></dxo-header-filter>
            <dxo-selection mode="single"></dxo-selection>
            <dxo-filter-panel [visible]="true"></dxo-filter-panel>
            <dxi-column dataField="area"
                        caption="P-Unit"
                        dataType="string"></dxi-column>

            <dxi-column dataField="assetidname"
                        caption="Asset Id"
                        sortOrder="asc"
                        dataType="string"
                        [cellTemplate]="'assetdetails'"></dxi-column>

            <dxi-column dataField="assetdescription"
                        caption="Asset Description"
                        dataType="string"
                        width="200"></dxi-column>

            <dxi-column dataField="inspectiontype"
                        caption="Inspection Type"
                        dataType="string"></dxi-column>
            <dxi-column dataField="completedinspectiondue"
                        caption="Completed Inspection Due"
                        dataType="date"></dxi-column>
            <dxi-column dataField="inspectiondate"
                        caption="Inspection Date"
                        dataType="date"></dxi-column>

            <dxi-column dataField="result"
                        caption="Result"
                        dataType="string"></dxi-column>
            <dxi-column dataField="planoperationid"
                        caption="Operation Id"
                        dataType="string"
                        [visible]="false"
                        [showInColumnChooser]="false"></dxi-column>

            <dxi-column dataField="nextinspectiondue"
                        caption="Next Inspection Due"
                        dataType="date"></dxi-column>
            <dxi-column dataField="scheduletype"
                        caption="Schedule Type"
                        dataType="string"></dxi-column>

            <dxi-column dataField="schedulestatus"
                        caption="Schedule Status"
                        dataType="string"></dxi-column>
            <dxi-column dataField="inspectionstatus"
                        caption="Inspection Status"
                        dataType="string"></dxi-column>


            <dxi-column dataField="inspectionassetcategory"
                        caption="Asset Category"
                        dataType="string"
                        [visible]="false"></dxi-column>
            <dxi-column dataField="riskclass"
                        caption="Risk Class"
                        dataType="string"
                        [visible]="false"></dxi-column>
            <dxi-column dataField="inspectionstate"
                        caption="Inspection State"
                        dataType="string"
                        [visible]="false"></dxi-column>

            <dxi-column dataField="frequency"
                        caption="Current Schedule Frequency"
                        dataType="string"
                        [visible]="false"></dxi-column>
            <dxi-column dataField="scheduleid"
                        caption="Schedule id"
                        dataType="string"
                        [showInColumnChooser]="false"
                        [visible]="false"></dxi-column>
            <dxi-column dataField="nextdate"
                        caption="Next Date"
                        dataType="date"
                        [showInColumnChooser]="false"
                        [visible]="false"></dxi-column>
            <dxi-column dataField="lastdate"
                        caption="Last Date"
                        dataType="date"
                        [showInColumnChooser]="false"
                        [visible]="false"></dxi-column>
            <dxi-column dataField="inspectiondue"
                        caption="Inspection Due"
                        dataType="date"
                        [showInColumnChooser]="false"
                        [visible]="false"></dxi-column>
            <dxi-column dataField="assetstatus"
                        caption="Asset Status"
                        dataType="string"
                        [visible]="false"></dxi-column>
            <dxi-column dataField="assetmanagementcategory"
                        caption="Asset Management Category"
                        dataType="string"
                        [visible]="false"></dxi-column>
            <dxi-column dataField="nextduedatenotes"
                        caption="Next Due Date Notes"
                        dataType="string"
                        [visible]="false"
                        [cellTemplate]="'nextDueDateNotesTemplate'"
                        [width]="300">
            </dxi-column>

            <div *dxTemplate="let data of 'nextDueDateNotesTemplate'">
                <p
                   style="padding: 4px; margin: 0; text-align: justify; white-space: normal;">
                    {{ data.value || 'No data available' }}
                </p>
            </div>
            <div *dxTemplate="let data of 'assetdetails'">
                <a *ngIf="data && data.value"
                   href="javascript://void"
                   (click)="assetSelectionChanged(data)">{{data.value}}</a>
            </div>
        </dx-data-grid>
        <div *ngIf="isLoading"
             style="width: 100%; height: 100%;">
            <dx-load-panel #loadPanel
                           shadingColor="rgba(0,0,0,0.4)"
                           [position]="{ of: '.gridload' }"
                           [(visible)]="isLoading"
                           [showIndicator]="true"
                           [showPane]="true"
                           [shading]="true"
                           [hideOnOutsideClick]="false">
            </dx-load-panel>
        </div>
    </div>
</div>
<div *ngIf="!canShowInspectionList"
     class="center-text">
    <p>
        <strong> Inspection/Schedule not found or you may not access to this
            Inspection/Schedule
        </strong>
    </p>

</div>