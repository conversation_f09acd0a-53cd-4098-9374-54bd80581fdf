using System;
using System.Linq;
using System.Threading.Tasks;
using ClientPortal.Shared.Extensions;
using ClientPortal.Shared.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.DependencyInjection;
using OrderTracking.API.Authorization.Requirements;
using OrderTracking.API.Services;

namespace OrderTracking.API.Authorization.Handlers
{
    /// <summary>
    ///     Class to handle authorization around district assignment by one UserProfile on another.
    /// </summary>
    public class
        DistrictAssignmentAuthorizationHandler : AuthorizationHandler<DistrictAssignmentRequirement, UserProfile>
    {
        private readonly IUserProfilesService _serviceProvider;

        /// <summary>
        ///     Constructor.
        /// </summary>
        /// <param name="serviceProvider"></param>
        public DistrictAssignmentAuthorizationHandler(IUserProfilesService serviceProvider)
        {
            _serviceProvider = serviceProvider;
        }

        /// <summary>
        ///     Handle the requirement.
        /// </summary>
        /// <param name="context"></param>
        /// <param name="requirement"></param>
        /// <param name="resource"></param>
        /// <returns></returns>
        protected override async Task HandleRequirementAsync(
            AuthorizationHandlerContext context,
            DistrictAssignmentRequirement requirement,
            UserProfile resource
        )
        {
            if (context == null) throw new ArgumentNullException(nameof(context));
            if (resource == null) throw new ArgumentNullException(nameof(resource));

            if (context.User.Identity?.Name != null)
                await AuthorizeChangesToDistrictIds(context, requirement, resource);
            else
                context.Fail();
        }

        private async Task AuthorizeChangesToDistrictIds(AuthorizationHandlerContext context,
            IAuthorizationRequirement requirement, UserProfile resource)
        {
            
            
            // Get current user profile
           // var assigner = await _serviceProvider.GetAsync(context.User.Identity?.Name);
            var assigner = await _serviceProvider.GetAsync(context.User.Identity?.Name.ToLowerInvariant());

            // Get user profile before change
            var originalUserProfile = await _serviceProvider.GetAsync(resource.Id);

            // See if current user is trying to assign new district ids to user profile
            var newDistricts = resource.DistrictIds.Except(originalUserProfile.DistrictIds).ToList();
            var removedDistricts = originalUserProfile.DistrictIds.Except(resource.DistrictIds).ToList();

            var districtIdsChanged = newDistricts.Any() || removedDistricts.Any();

            if (districtIdsChanged)
            {
                if (!originalUserProfile.IsTeamEmployee) context.Fail();
                
                if (assigner.HasRole("app:admin"))
                    context.Succeed(requirement);
                else if (assigner.IsModuleAdmin() &&
                         (assigner.HasDistricts(newDistricts) || assigner.HasRole("wmo:assigner")))
                    context.Succeed(requirement);
                else
                    context.Fail();
            }

            context.Succeed(requirement);
        }
    }
}