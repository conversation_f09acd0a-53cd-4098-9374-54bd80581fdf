<dx-popup [(visible)]="submissionPopupVisible"
          [width]="1200"
          [height]="700"
          [showTitle]="true"
          [dragEnabled]="false"
          [showCloseButton]="true">
    <dxi-toolbar-item toolbar="top"
                      [text]="submissionPopupTitle"
                      location="center"></dxi-toolbar-item>

    <ng-container *ngIf="submissionPopupVisible">

        <app-client-data-submission [initialAnomaly]="initialAnomaly"
                                    (clientSubmissionTitleValueChange)="clientSubmissionTitleValueChange($event)"
                                    (formSubmittedValueChange)="clientDataFormSubmitted($event)">
        </app-client-data-submission>
    </ng-container>
</dx-popup>
<div class="data-grid">
    <dx-data-grid [wordWrapEnabled]="true"
                  [rowAlternationEnabled]="true"
                  [dataSource]="inspectionanomalies"
                  [showColumnLines]="false"
                  [showRowLines]="true"
                  [showBorders]="true"
                  #dataGrid>
        <dxo-header-filter [visible]="true"></dxo-header-filter>
        <dxo-filter-row [visible]="true"></dxo-filter-row>

        <dxo-filter-panel [visible]="true"></dxo-filter-panel>
        <dxo-export [enabled]="true"></dxo-export>
        <dxi-column dataField="anomalypriority"
                    caption="Priority"></dxi-column>
        <dxi-column dataField="anomalytype"
                    caption="Anomaly type"></dxi-column>
        <dxi-column dataField="anomalydescription"
                    caption="Description"
                    [calculateCellValue]="formatAnomalyDescription"
                    [width]="200">
        </dxi-column>
        <dxi-column dataField="proposedrecommemendation"
                    caption="Proposed recommendation"
                    [calculateCellValue]="formatAnomalyProposedRecom"
                    [width]="200"></dxi-column>
        <dxi-column dataField="resolutionstate"
                    caption="Approval State"></dxi-column>
        <dxi-column dataField="anomaly"
                    caption="Anomaly N."
                    [cellTemplate]="anomalyCellTemplate"></dxi-column>
    </dx-data-grid>