﻿using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.DependencyInjection;
using OrderTracking.API.Authorization.Requirements;
using OrderTracking.API.Services;

namespace OrderTracking.API.Authorization.Handlers
{
    /// <summary>
    ///     Handles the IsTeamEmployeeRequirement
    /// </summary>
    public class IsTeamEmployeeHandler : AuthorizationHandler<IsTeamEmployeeRequirement>
    {
        #region Fields and Constants

        private readonly IUserProfilesService _serviceProvider;

        #endregion Fields and Constants

        #region Constructors

        /// <summary>
        ///     Constructs an IsTeamEmployeeHandler, injecting service provider
        /// </summary>
        /// <param name="serviceProvider"></param>
        public IsTeamEmployeeHandler(IUserProfilesService serviceProvider)
        {
            _serviceProvider = serviceProvider;
        }

        #endregion Constructors

        /// <summary>
        ///     Handle the <see cref="IsTeamEmployeeRequirement"/>
        /// </summary>
        /// <param name="context"></param>
        /// <param name="requirement"></param>
        /// <returns></returns>
        protected override async Task HandleRequirementAsync(AuthorizationHandlerContext context,
            IsTeamEmployeeRequirement requirement)
        {
            if (context == null) throw new ArgumentNullException(nameof(context));

            if (!context.User.Identity.IsAuthenticated)
            {
                context.Fail();
                return;
            }

            var email = context.User.Identity.Name.ToLower();

            
            
            var user = await _serviceProvider.GetAsync(email);
            if (user == null) return;

            if (user.IsTeamEmployee) context.Succeed(requirement);
        }
    }
}