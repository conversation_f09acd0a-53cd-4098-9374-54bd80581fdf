﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
 <packageSources>
     <clear />
     <add key="nuget.org" value="https://api.nuget.org/v3/index.json" protocolVersion="3" />
     <add key="DevExpress" value="https://nuget.devexpress.com/XHyuzNND9afihxCrbhh3XCFHY2b5lqhOmKdXkj2vhWtdb3sYpm/api" protocolVersion="23.2.5" />
     <!-- Temporarily disabled GitLab source due to authentication issues during migration -->
     <!-- <add key="gitlab" value="https://gitlab.com/api/v4/projects/55854683/packages/nuget/index.json" /> -->
 </packageSources>
 <packageSourceCredentials>
     <gitlab>
         <add key="Username" value="deepikakhandelwal" />
         <add key="ClearTextPassword" value="**************************" />
     </gitlab>
 </packageSourceCredentials>
</configuration>
