﻿// DEPRECATED: This Firebase-based AuthHistoryService has been replaced by AuthHistoryCosmosService
// Commented out during Firebase-to-Azure migration cleanup
/*
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ClientPortal.Shared.Models;
using Google.Cloud.Firestore;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using OrderTracking.API.Models;

namespace OrderTracking.API.Services
{
    public class AuthHistoryService : IAuthHistoryService
    {
        private readonly CollectionReference _collection;

        public AuthHistoryService(IOptions<Connections> options)
        {
            if (options == null) throw new ArgumentNullException(nameof(options));

            FirestoreDb firestoreDb = new FirestoreDbBuilder
            {
                ProjectId = options.Value.ProjectId,
                DatabaseId = options.Value.DatabaseName
            }.Build();

            _collection = firestoreDb.Collection(options.Value.AuthHistory);
        }

        public async Task<IEnumerable<ChangeEvent>> GetItemsAsync()
        {
            var querySnapshot = await _collection.GetSnapshotAsync();

            return querySnapshot.Documents
           .Select(document => document.ConvertTo<ChangeEvent>())
                                   .OrderByDescending(r => r.CreatedAt)
                                   .ToList();
        }

        public async Task<ActionResult<ChangeEvent>> GetItemAsync(string id)
        {
            var documentSnapshot = await _collection.Document(id).GetSnapshotAsync();

            if (!documentSnapshot.Exists)
            {
                return null;
            }

            var authHistoryItem = documentSnapshot.ConvertTo<ChangeEvent>();
            return authHistoryItem;
        }

        public async Task<string> AddItemAsync(ChangeEvent changeEvent)
        {
            if (changeEvent == null) throw new ArgumentNullException(nameof(changeEvent));
            if (changeEvent.Old == null && changeEvent.New == null)
                throw new InvalidOperationException(
                    "Cannot create ChangeEvent without old and/or new snapshots of record being changed.");

            if (changeEvent.Id == null )
                throw new InvalidOperationException("ChangeEvent Id cannot be null.");

            DocumentReference docRef = _collection.Document(changeEvent.Id.ToString());
            WriteResult writeResult = await docRef.SetAsync(changeEvent);
            Console.WriteLine(writeResult.UpdateTime);

            return docRef.Id;
        }

        public async Task RemoveAsync(string id)
        {
            var documentReference = _collection.Document(id);
            var snapshot = await documentReference.GetSnapshotAsync();
            if (snapshot.Exists)
            {
                await documentReference.DeleteAsync();
            }
        }
    }
}
*/
