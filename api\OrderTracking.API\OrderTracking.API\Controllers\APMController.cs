using APMWebDataInterface.DataModel;
using APMWebDataInterface.ExampleDataModel;
using ClientPortal.Shared.Extensions;
using DevExtreme.AspNet.Data;
//using FirebaseAdmin.Auth;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using OrderTracking.API.Extensions.APM;
using OrderTracking.API.Interfaces.Services;
using OrderTracking.API.Models;
using OrderTracking.API.Models.APM;
using OrderTracking.API.Services;
using OrderTracking.API.Services.APM;
using System;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using PublishUpdateObject = OrderTracking.API.Models.APM.PublishUpdateObject;
using UserProfile = ClientPortal.Shared.Models.UserProfile;

namespace OrderTracking.API.Controllers
{
    /// <summary>
    ///     Controller for APM related resources
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class APMController : ControllerBase
    {
        private readonly IUserProfilesService _userProfiles;
        private readonly APM_WebDataInterface _apm;
        private readonly IAssetService _assets;
        private readonly IJasperSoftService _jasperSoft;
        private readonly ILocationService _locations;
        private readonly ILogger<APMController> _logger;
        private readonly IAPMBlobStorageService _oneInsightStorage;
        private readonly IProjectService _projects;
        private readonly IWorkOrderService _workOrders;
        private readonly ITasksService _tasks;
        private static bool ShouldForbidUpdate(UserProfile user) =>
            !user.HasRole("apm:edit") &&
            !user.HasRole("apm:qaqc") &&
            !user.HasRole("app:admin") &&
            !user.HasRole("apm:admin");

        /// <summary>
        ///     Constructor for APMController
        /// </summary>
        /// <param name="apm"></param>
        /// <param name="oneInsightStorage"></param>
        /// <param name="logger"></param>
        /// <param name="jasperSoft"></param>
        /// <param name="projects"></param>
        /// <param name="assets"></param>
        /// <param name="workOrders"></param>
        /// <param name="locations"></param>
        /// <param name="tasks"></param>
        /// <param name="userProfiles"></param>
        public APMController(
            APM_WebDataInterface apm,
            IAPMBlobStorageService oneInsightStorage,
            ILogger<APMController> logger,
            IJasperSoftService jasperSoft,
            IProjectService projects,
            IAssetService assets,
            IWorkOrderService workOrders,
            ILocationService locations,
            ITasksService tasks,
            IUserProfilesService userProfiles
        )
        {
            _apm = apm;
            _userProfiles = userProfiles;
            _oneInsightStorage = oneInsightStorage;
            _logger = logger;
            _jasperSoft = jasperSoft;
            _projects = projects;
            _assets = assets;
            _workOrders = workOrders;
            _locations = locations;
            _tasks = tasks;
        }

        /// <summary>
        ///     Get Azure AD Token for APM authentication (migrated from Firebase)
        /// </summary>
        /// <returns></returns>
        [HttpGet("AzureToken")]
        [Authorize]
        public async Task<IActionResult> GetAzureToken()
        {
            try
            {
                // Get the current user's access token from the HTTP context
                var accessToken = await HttpContext.GetTokenAsync("access_token");
                
                if (string.IsNullOrEmpty(accessToken))
                {
                    // If access_token is not available, get it from Authorization header
                    var authHeader = HttpContext.Request.Headers["Authorization"].FirstOrDefault();
                    if (authHeader != null && authHeader.StartsWith("Bearer "))
                    {
                        accessToken = authHeader.Substring("Bearer ".Length).Trim();
                    }
                }

                if (string.IsNullOrEmpty(accessToken))
                {
                    return Unauthorized("No valid token found");
                }

                // Return the token along with user information
                var tokenInfo = new
                {
                    Token = accessToken,
                    UserEmail = CurrentUserEmail?.ToLower(),
                    ExpiresAt = DateTime.UtcNow.AddHours(1), // Typical Azure AD token lifetime
                    TokenType = "Bearer"
                };

                return Ok(tokenInfo);
            }
            catch (Exception ex)
            {
                return BadRequest($"Error retrieving Azure token: {ex.Message}");
            }
        }

        /// <summary>
        ///     Gets all projects
        /// </summary>
        /// <returns></returns>
        [HttpGet("Projects")]
        [Authorize(Policy = "APM - View")]
        public async Task<ActionResult> GetProjects(DataSourceLoadOptions options)
        {
            var email = CurrentUserEmail?.ToLower();
            var user = await _userProfiles.GetAsync(email);
            if (user == null)
            {
                return Unauthorized();
            }
            var projects = _projects.Get(email);
            var queryable = projects
                .Where(project => project.businessUnitId.CurrentValue == user.SelectedBusinessUnit)
                .Select(project => _projects.BuildProjectVM(project))
                .AsQueryable();
            var result = DataSourceLoader.Load(queryable, options);
            return Ok(result);
        }

        /// <summary>
        ///     Gets a all project Names with their IDs
        /// </summary>
        /// <returns></returns>
        [HttpGet("Projects/names")]
        [Authorize(Policy = "APM - View")]
        public async Task<ActionResult> GetProjectNames(DataSourceLoadOptions options)
        {
            var email = CurrentUserEmail?.ToLower();
            var user = await _userProfiles.GetAsync(email);
            if (user == null)
            {
                return Unauthorized();
            }

            var projects = _projects.Get(email);
            var queryable = projects
                .Where(project => project.businessUnitId.CurrentValue == user.SelectedBusinessUnit)
                .Select(data => new { id = data.id, name = data.name.CurrentValue, LocationId = data.locationId })
            .AsQueryable();

            var result = DataSourceLoader.Load(queryable, options);
            return Ok(result);
        }

        /// <summary>
        ///     Gets a project by id
        /// </summary>
        /// <param name="projectId"></param>
        /// <returns></returns>
        [HttpGet("Project/{projectId}")]
        [Authorize(Policy = "APM - View")]
        public IActionResult GetProject(string projectId)
        {
            var email = CurrentUserEmail?.ToLower();
            var project = _projects.Get(projectId, email);
            if (project == null) return NotFound();
            return Ok(_projects.BuildProjectVM(project));
        }

        /// <summary>
        ///     Post
        /// </summary>
        /// <returns></returns>
        [HttpPost("Project")]
        [Authorize(Policy = "APM - Edit")]
        public async Task<IActionResult> PostProject([FromBody] NewProjectTransportObject newProject)
        {
            var shouldForbid = await ShouldForbidUpdate();
            if (shouldForbid != null) return shouldForbid;

            var user = await _userProfiles.GetAsync(CurrentUserEmail);
            if (user == null) return Forbid();

            var project = await _projects.Create(newProject, CurrentUserEmail, user.SelectedBusinessUnit);

            return Ok(_projects.BuildProjectVM(project));
        }

        /// <summary>
        ///     Post Asset
        /// </summary>
        /// <returns></returns>
        [HttpPost("Asset")]
        [Authorize(Policy = "APM - Edit")]
        public async Task<IActionResult> PostAsset(NewAsset newAsset)
        {
            var shouldForbid = await ShouldForbidUpdate();
            if (shouldForbid != null) return shouldForbid;

            var email = CurrentUserEmail?.ToLower();

            var user = await _userProfiles.GetAsync(email);
            if (user == null) return Forbid();

            if (newAsset.Category == null) return BadRequest("Category is required");
            if (newAsset.LocationId == null)
            {
                if (newAsset.ProjectId == null) return BadRequest("Location/Project is required");

                var project = _projects.Get(newAsset.ProjectId, email);
                if (project == null) return BadRequest("Invalid Project ID");
                newAsset.LocationId = project.locationId;
            }

            var asset = await _assets.Create(newAsset, CurrentUserEmail, user.SelectedBusinessUnit);

            if (newAsset.ProjectId != null)
            {
                var project = _projects.Get(newAsset.ProjectId, email);
                if (project == null) return NotFound();
                await _projects.AddAssetToProject(project, asset.id, CurrentUserEmail);
            }

            return Ok(asset);
        }

        /// <summary>
        ///     Updates an asset from an <see cref="AssetVM"/> payload
        /// </summary>
        /// <param name="assetVM"></param>
        /// <returns></returns>
        [HttpPut("Asset")]
        [Authorize(Policy = "APM - Edit")]
        public async Task<IActionResult> UpdateAsset(AssetVM assetVM)
        {
            var shouldForbid = await ShouldForbidUpdate();
            if (shouldForbid != null) return shouldForbid;
            var email = CurrentUserEmail.ToLower();

            var asset = await _assets.Get(assetVM.Id, email);

            if (asset == null) return NotFound();

            await _assets.Update(asset, assetVM, email);

            return Ok(_assets.BuildAssetVM(asset));
        }

        /// <summary>
        ///     Post Asset
        /// </summary>
        /// <returns></returns>
        [HttpPut("AssetAccess")]
        [Authorize(Policy = "APM - Edit")]
        public async Task<IActionResult> UpdateAssetAccess(AssetAccessUpdate assetUpdate)
        {
            var shouldForbid = await ShouldForbidUpdate();
            if (shouldForbid != null) return shouldForbid;
            var email = CurrentUserEmail?.ToLower();

            var asset = await _assets.Get(assetUpdate.Id, email);

            if (asset == null) return NotFound();

            await _assets.Update(asset, assetUpdate, email);

            return Ok(asset);
        }

        /// <summary>
        ///     Gets all project and task activity submitted by technicians
        /// </summary>
        /// <param name="projectId"></param>
        /// <returns></returns>
        [HttpGet("AllActivity/{projectId}")]
        [Authorize(Policy = "APM - View")]
        public async Task<IActionResult> GetAllActivity(string projectId)
        {
            var shouldForbid = await ShouldForbidUpdate();
            if (shouldForbid != null) return shouldForbid;
            var email = CurrentUserEmail?.ToLower();
            if (email == null) return NotFound();

            var project = _projects.Get(projectId, email);
            if (project == null) return NotFound();

            var projectActivities = project.activities.CurrentEntries;

            var workOrders = await _workOrders.GetByProject(email, projectId);
            var tasks = workOrders.SelectMany(w => w.tasks);
            var taskActivities = tasks
                .SelectMany(t => t.activities
                    .CurrentEntries
                    .Select(activity => new
                    {
                        activity.activities,
                        displayName = activity.DisplayName,
                        activity.date,
                        activity.user,
                        createdBy = activity.CreatedBy,
                        createdTime = activity.CreatedTime,
                        databaseId = activity.DatabaseId,
                        workOrderNumber = t.clientWorkOrderNumber,
                        taskId = t.id
                    }));

            return Ok(new { projectActivities, taskActivities });
        }

        /// <summary>
        ///     Put ProjectActivity
        /// </summary>
        /// <returns></returns>
        [HttpPut("ProjectActivity")]
        [Authorize(Policy = "APM - Edit")]
        public async Task<IActionResult> UpdateProjectActivity(ActivityUpdate update)
        {
            var shouldForbid = await ShouldForbidUpdate();
            if (shouldForbid != null) return shouldForbid;
            var email = CurrentUserEmail?.ToLower();
            if (email == null) return Unauthorized();

            var project = _projects.Get(update.ProjectId, email);
            if (project == null) return NotFound();

            try
            {
                switch (update.ActivityType)
                {
                    case "project":
                        project.Update(update);
                        await project.SavePendingChanges(email);
                        break;
                    case "task":
                        var workOrders = await _workOrders.GetByProject(email, update.ProjectId);
                        var task = workOrders.SelectMany(w => w.tasks).SingleOrDefault(t => t.id == update.TaskID);
                        if (task == null) return NotFound();
                        task.Update(update);
                        await task.SavePendingChanges(email);
                        break;
                    default: throw new InvalidOperationException($"Unknown activity type: {update.ActivityType}");
                }
            }
            catch (ProjectActivityNotFoundException e)
            {
                _logger.LogWarning(e.Message);
                return NotFound();
            }

            return Ok(_projects.BuildProjectVM(project));
        }

        /// <summary>
        ///     Add and/or remove assets from a project
        /// </summary>
        /// <param name="addRemoveProjectAsset"></param>
        /// <returns></returns>
        [HttpPut("AddRemoveProjectAsset")]
        [Authorize(Policy = "APM - Edit")]
        public async Task<IActionResult> AddRemoveProjectAsset(AddRemoveProjectAsset addRemoveProjectAsset)
        {
            var shouldForbid = await ShouldForbidUpdate();
            if (shouldForbid != null) return shouldForbid;
            var email = CurrentUserEmail?.ToLower();
            if (email == null) return Unauthorized();

            var project = _projects.Get(addRemoveProjectAsset.ProjectId, email);
            if (project == null) return NotFound();

            project.Update(addRemoveProjectAsset);

            await project.SavePendingChanges(email);

            return Ok(_projects.BuildProjectVM(project));
        }

        /// <summary>
        ///     Updates Asset PPE related info for an asset.
        /// </summary>
        /// <param name="id"></param>
        /// <param name="assetPPEUpdate"></param>
        /// <returns></returns>
        [HttpPut("AssetPPE/{id}")]
        [Authorize(Policy = "APM - Edit")]
        public async Task<IActionResult> UpdateAssetPPE(string id, AssetPPEUpdate assetPPEUpdate)
        {
            var email = CurrentUserEmail?.ToLower();
            var shouldForbid = await ShouldForbidUpdate();
            if (shouldForbid != null) return shouldForbid;
            var asset = await _assets.Get(id, email);
            if (asset == null) return NotFound();

            await _assets.Update(asset, assetPPEUpdate, CurrentUserEmail);

            return Ok(asset);
        }

        /// <summary>
        ///     Updates the walk down task of a work order when the asset is a Vessel (510)
        /// </summary>
        /// <param name="assetDetails"></param>
        /// <returns></returns>
        [HttpPut("FiveTenAssetDetails")]
        [Authorize(Policy = "APM - Edit")]
        public async Task<IActionResult> UpdateFiveTenAssetDetails(FiveTenAssetDetailsUpdate assetDetails)
        {
            var email = CurrentUserEmail?.ToLower();
            var shouldForbid = await ShouldForbidUpdate();
            if (shouldForbid != null) return shouldForbid;
            var workOrder = await _workOrders.Get(assetDetails.WorkOrderId, assetDetails.ProjectId, email);

            if (workOrder == null) return NotFound();

            try
            {
                await _workOrders.Update(workOrder, assetDetails, CurrentUserEmail);
            }
            catch (TasksNotFoundException e)
            {
                _logger.LogWarning(e.Message);
                return NotFound();
            }

            return Ok();
        }

        /// <summary>
        ///     Updates a work order and project if the TEAMProjectNumber or APMProjectNumber is included in the payload
        /// </summary>
        /// <param name="workOrderUpdate"></param>
        /// <returns></returns>
        [HttpPut("WorkOrder")]
        [Authorize(Policy = "APM - Edit")]
        public async Task<IActionResult> PutWorkOrder(WorkOrderTransportObject workOrderUpdate)
        {
            var shouldForbid = await ShouldForbidUpdate();
            if (shouldForbid != null) return shouldForbid;
            var email = CurrentUserEmail?.ToLower();

            var workOrder = await _workOrders.Get(workOrderUpdate.ID, workOrderUpdate.ProjectID, email);


            if (workOrder == null) return NotFound();

            await _workOrders.Update(workOrder, workOrderUpdate, email);

            if (workOrderUpdate.TEAMProjectNumber != null || workOrderUpdate.APMProjectNumber != null)
            {
                var project = _projects.Get(workOrder.projectId, email);
                if (project != null) await _projects.Update(project, workOrderUpdate, email);
            }

            return Ok(workOrder);
        }

        /// <summary>
        ///     Updates a work order and project if the TEAMProjectNumber or APMProjectNumber is included in the payload
        /// </summary>
        /// <param name="workOrderUpdate"></param>
        /// <returns></returns>
        [HttpPut("FieldWorkCompleted")]
        [Authorize(Policy = "APM - ChangePublishedState")]
        public async Task<IActionResult> UpdateFieldWorkCompleted(FieldWorkCompletedTransportObject fieldWorkCompletedUpdate)
        {
            var shouldForbid = await ShouldForbidUpdate();
            if (shouldForbid != null) return shouldForbid;

            var email = CurrentUserEmail?.ToLower();

            var workOrder = await _workOrders.Get(fieldWorkCompletedUpdate.Id, fieldWorkCompletedUpdate.ProjectId, email);


            if (workOrder == null) return NotFound();

            var parsed = DateTime.TryParse(workOrder.fieldWorkCompleted.CurrentValue, out var fieldWorkCompleted);
            var shouldUpdateFieldWorkCompleted = (parsed && workOrder.fieldWorkCompleted.CurrentValue != null ||
                                                  !parsed && workOrder.fieldWorkCompleted.CurrentValue == null) &&
                                                 fieldWorkCompletedUpdate.FieldWorkCompletedDate != fieldWorkCompleted;
            if (shouldUpdateFieldWorkCompleted)
                workOrder.fieldWorkCompleted.SetValue(fieldWorkCompletedUpdate.FieldWorkCompletedDate);

            await workOrder.SavePendingChanges(email);

            return Ok(workOrder);
        }

        /// <summary>
        ///     Deletes a Visual Inspection photo
        /// </summary>
        /// <param name="visualInspectionPhoto"></param>
        /// <returns></returns>
        [HttpDelete("VisualInspectionPhoto")]
        [Authorize(Policy = "APM - Edit")]
        public async Task<IActionResult> DeleteVisualInspectionPhoto(
            VisualInspectionPhotoTransport visualInspectionPhoto)
        {
            var shouldForbid = await ShouldForbidUpdate();
            if (shouldForbid != null) return shouldForbid;
            await _workOrders.DeleteVisualInspectionPhoto(visualInspectionPhoto, CurrentUserEmail?.ToLower());

            return Ok();
        }

        /// <summary>
        ///     Updates the description of a Visual Inspection Photo
        /// </summary>
        /// <param name="visualInspectionPhoto"></param>
        /// <returns></returns>
        [HttpPut("VisualInspectionPhotoDescription")]
        [Authorize(Policy = "APM - Edit")]
        public async Task<IActionResult> UpdateVisualInspectionPhotoDescription(
            VisualInspectionPhotoTransport visualInspectionPhoto)
        {
            var shouldForbid = await ShouldForbidUpdate();
            if (shouldForbid != null) return shouldForbid;
            await _workOrders.UpdateVisualInspectionPhotoDescription(visualInspectionPhoto, CurrentUserEmail?.ToLower());

            return NoContent();
        }

        /// <summary>
        ///     Updates a work order's visual inspection info
        /// </summary>
        /// <returns></returns>
        [HttpPut("WorkOrder/VisualInspection")]
        [Authorize(Policy = "APM - Edit")]
        public async Task<IActionResult> PutWorkOrderVisualInspection([FromBody] VisualInspection inspection)
        {
            var shouldForbid = await ShouldForbidUpdate();
            if (shouldForbid != null) return shouldForbid;
            var email = CurrentUserEmail?.ToLower();

            var workOrder = await _workOrders.Get(inspection.WorkOrderId, inspection.ProjectId, email);

            if (workOrder == null) return NotFound();

            try
            {
                await _workOrders.Update(workOrder, inspection, email);
            }
            catch (Exception e)
            {
                if (e is not VisualInspectionSectionsNotFoundException &&
                    e is not VisualInspectionNotFoundException) throw;

                _logger.LogWarning(e.Message);
                return NotFound();
            }

            return Ok(workOrder);
        }

        /// <summary>
        ///     Updates a project
        /// </summary>
        /// <returns></returns>
        [HttpPut("Project")]
        [Authorize(Policy = "APM - Edit")]
        public async Task<IActionResult> PutProject([FromBody] ProjectTransportObject projectUpdate)
        {
            var shouldForbid = await ShouldForbidUpdate();
            if (shouldForbid != null) return shouldForbid;
            var email = CurrentUserEmail?.ToLower();

            var project = _projects.Get(projectUpdate.Id, email);

            if (project == null) return NotFound();

            var location = await _locations.Get(project.locationId, email);

            if (location == null) return NotFound();

            await _projects.Update(project, projectUpdate, email);

            await _locations.Update(location, projectUpdate, email);

            return Ok(_projects.BuildProjectVM(project));
        }

        [HttpPost("Location")]
        [Authorize(Policy = "APM - Edit")]
        public async Task<IActionResult> CreateLocation(LocationTransportObject locationTransport)
        {
            var shouldForbid = await ShouldForbidUpdate();
            if (shouldForbid != null) return shouldForbid;

            var user = await _userProfiles.GetAsync(CurrentUserEmail);
            if (user == null) return Forbid();

            var location = await _locations.Create(locationTransport, CurrentUserEmail, user.SelectedBusinessUnit);

            return Ok(location);
        }

        /// <summary>
        ///     Put Location
        /// </summary>
        /// <returns></returns>
        [HttpPut("Location")]
        [Authorize(Policy = "APM - Edit")]
        public async Task<IActionResult> PutLocation([FromBody] LocationTransportObject locationTransport)
        {
            var email = CurrentUserEmail?.ToLower();
            var shouldForbid = await ShouldForbidUpdate();
            if (shouldForbid != null) return shouldForbid;

            var location = await _locations.Get(locationTransport.Id, email);

            if (location == null) return NotFound();

            await _locations.Update(location, locationTransport, CurrentUserEmail);

            return Ok(location);
        }

        /// <summary>
        ///     Gets all locations
        /// </summary>
        /// <returns></returns>
        [HttpGet("Locations")]
        [Authorize(Policy = "APM - View")]
        public async Task<IActionResult> GetLocations(DataSourceLoadOptions options)
        {
            var email = CurrentUserEmail?.ToLower();
            var user = await _userProfiles.GetAsync(email);
            if (user == null)
            {
                return Unauthorized();
            }

            var locations = await _locations.Get(email);
            var queryable = locations
                .Where(location => location.businessUnitId?.CurrentValue == user.SelectedBusinessUnit)
                .AsQueryable()
                .OrderBy(location => location.name.CurrentValue);
            var result = DataSourceLoader.Load(queryable, options);
            return Ok(result);
        }

        /// <summary>
        ///     Gets a location by id
        /// </summary>
        /// <param name="locationId"></param>
        /// <returns></returns>
        [HttpGet("Location/{locationId}")]
        [Authorize(Policy = "APM - View")]
        public async Task<IActionResult> GetLocation(string locationId)
        {
            var email = CurrentUserEmail?.ToLower();
            var location = await _locations.Get(locationId, email);
            if (location == null) return NotFound();
            return Ok(location);
        }

        /// <summary>
        ///     Gets assets associated with a project
        /// </summary>
        /// <param name="projectId"></param>
        /// <returns></returns>
        [HttpGet("ProjectAssets/{projectId}")]
        [Authorize(Policy = "APM - View")]
        public async Task<IActionResult> GetAssetsForProject(string projectId)
        {
            var email = CurrentUserEmail?.ToLower();
            var assets = await _assets.GetByProjects(email, projectId);
            return Ok(assets);
        }

        /// <summary>
        ///     Gets assets associated with any of the projects provided by the projectIds
        /// </summary>
        /// <param name="projectIds"></param>
        /// <returns></returns>
        [HttpGet("ProjectAssets")]
        [Authorize(Policy = "APM - View")]
        public async Task<IActionResult> GetAssetsForProjects([FromQuery(Name = "projectIds")] string[] projectIds)
        {
            var email = CurrentUserEmail?.ToLower();
            var assets = await _assets.GetByProjects(email, projectIds);
            return Ok(assets);
        }

        /// <summary>
        ///     Get assets by the filter provided by <see cref="DataSourceLoadOptions"/>
        /// </summary>
        /// <param name="options"></param>
        /// <returns></returns>
        [HttpGet("LoadAssets")]
        [Authorize(Policy = "APM - View")]
        public async Task<IActionResult> LoadAssets(DataSourceLoadOptions options)
        {
            var email = CurrentUserEmail?.ToLower();
            var user = await _userProfiles.GetAsync(email);
            if (user == null) return Forbid();
            var assets = await _assets.Get(email);
            var queryable = assets
                .Where(asset => asset.businessUnitId.CurrentValue == user.SelectedBusinessUnit)
                .Select(a => _assets.BuildAssetVM(a))
                .AsQueryable();
            // ReSharper disable once MethodHasAsyncOverload
            var result = DataSourceLoader.Load(queryable, options);
            return Ok(result);
        }

        /// <summary>
        ///     Get an individual asset by id
        /// </summary>
        /// <param name="assetId"></param>
        /// <returns></returns>
        [HttpGet("Assets/{assetId}")]
        [Authorize(Policy = "APM - View")]
        public async Task<IActionResult> GetAsset(string assetId)
        {
            var email = CurrentUserEmail?.ToLower();
            var asset = await _assets.Get(assetId, email);
            if (asset == null) return NotFound();
            return Ok(_assets.BuildAssetVM(asset));
        }

        /// <summary>
        ///     Get Work Orders by Asset Id and some other Work Order Filters
        /// </summary>
        /// <param name="assetId"></param>
        /// <returns></returns>
        [HttpGet("Assets/{assetId}/WorkOrders")]
        [Authorize(Policy = "APM - View")]
        public async Task<IActionResult> GetWorkOrdersByAsset(DataSourceLoadOptions options, string assetId)
        {
            var email = CurrentUserEmail?.ToLower();
            var user = await _userProfiles.GetAsync(email);
            var asset = await _assets.Get(assetId, email);
            if (asset == null)
            {
                return NoContent();
            }

            if (asset.businessUnitId.CurrentValue != user.SelectedBusinessUnit)
            {
                return NoContent();
            }

            var workOrderIds = asset.workOrdersContainedIn;

            if(!workOrderIds?.Any() ?? true)
            {
                return NoContent();
            }

            var workOrders = await _workOrders.GetByAsset(asset, workOrderIds, email);

            var queryable = workOrders
                .Select(wo => _workOrders.BuildWorkOrderVM(wo))
                .AsQueryable();

            var result = DataSourceLoader.Load(queryable, options);
            return Ok(result);
        }

        /// <summary>
        ///     Gets all assets for a specified project
        /// </summary>
        /// <returns></returns>
        [HttpGet("LoadProjectAssets/{projectId}")]
        [Authorize(Policy = "APM - View")]
        public async Task<IActionResult> LoadProjectAssets(DataSourceLoadOptions options, string projectId)
        {
            var email = CurrentUserEmail?.ToLower();
            var user = await _userProfiles.GetAsync(email);
            if (user == null) return Forbid();
            var assets = await _assets.GetByProjects(email, projectId);
            var queryable = assets
                .Where(asset => asset.businessUnitId.CurrentValue == user.SelectedBusinessUnit)
                .Select(a => _assets.BuildAssetVM(a))
                .AsQueryable();
            var result = DataSourceLoader.Load(queryable, options);
            return Ok(result);
        }

        /// <summary>
        ///     Creates a work order
        /// </summary>
        /// <param name="newWorkOrder"></param>
        /// <returns></returns>
        [HttpPost("WorkOrders")]
        [Authorize(Policy = "APM - Edit")]
        public async Task<IActionResult> CreateWorkOrder(NewWorkOrder newWorkOrder)
        {
            var email = CurrentUserEmail?.ToLower();
            var shouldForbid = await ShouldForbidUpdate();
            if (shouldForbid != null) return shouldForbid;
            var asset = await _assets.Get(newWorkOrder.AssetDatabaseID, email);

            var user = await _userProfiles.GetAsync(CurrentUserEmail);
            if (user == null) return Forbid();

            if (asset == null) return NotFound($"Asset with ID {newWorkOrder.AssetDatabaseID} not found.");

            var project = _projects.Get(newWorkOrder.ProjectId, email);

            if (project == null) return NotFound("Asset is not associated with any project");

            var workOrder = await _workOrders.Create(newWorkOrder, asset, project, CurrentUserEmail, user.SelectedBusinessUnit);

            return Ok(workOrder);
        }

        /// <summary>
        ///     Gets assets at a location via the location's id
        /// </summary>
        /// <param name="locationId"></param>
        /// <returns></returns>
        [HttpGet("LocationAssets/{locationId}")]
        [Authorize(Policy = "APM - View")]
        public async Task<IActionResult> GetAssetsForLocation(string locationId)
        {
            var email = CurrentUserEmail?.ToLower();
            var assets = await _apm.GetAssetsAtLocation(new[] { locationId }, email);
            return Ok(assets);
        }

        /// <summary>
        ///     Gets work orders associated to a particular project
        /// </summary>
        /// <param name="projectId"></param>
        /// <returns></returns>
        [HttpGet("WorkOrders/{projectId}")]
        [Authorize(Policy = "APM - View")]
        public async Task<IActionResult> GetWorkOrders(string projectId)
        {
            var email = CurrentUserEmail?.ToLower();
            var workOrders = await _workOrders.GetByProject(email, projectId);
            return Ok(workOrders);
        }

        [HttpGet("LoadWorkOrder/{workOrderId}")]
        [Authorize(Policy = "APM - View")]
        public async Task<IActionResult> LoadWorkOrder(string workOrderId)
        {
            var email = CurrentUserEmail?.ToLower();
            var workOrder = await _workOrders.Get(workOrderId, email);
            return Ok(_workOrders.BuildWorkOrderVM(workOrder));
        }

        /// <summary>
        ///     Gets a specific work order by its id
        /// </summary>
        /// <param name="workOrderId"></param>
        /// <returns></returns>
        [HttpGet("WorkOrder/{workOrderId}")]
        [Authorize(Policy = "APM - View")]
        public async Task<IActionResult> GetWorkOrder(string workOrderId)
        {
            var email = CurrentUserEmail?.ToLower();
            var workOrder = await _workOrders.Get(workOrderId, email);

            if (workOrder == null) return NotFound();

            return Ok(workOrder);
        }

        /// <summary>
        ///     Gets a specific Work Order by its id and its Project ID
        /// </summary>
        /// <param name="projectId"></param>
        /// <param name="workOrderId"></param>
        /// <returns></returns>
        [HttpGet("Projects/{projectId}/WorkOrders/{workOrderId}")]
        [Authorize(Policy = "APM - View")]
        public async Task<IActionResult> GetWorkOrder(string projectId, string workOrderId)
        {
            var email = CurrentUserEmail?.ToLower();
            var workOrder = await _workOrders.Get(workOrderId, projectId, email);
            if (workOrder == null)
            {
                return NotFound();
            }

            return Ok(workOrder);
        }

        /// <summary>
        ///     Gets the users in the APM system
        /// </summary>
        /// <returns></returns>
        [HttpGet("Users")]
        [Authorize(Policy = "APM - View")]
        public IActionResult GetUsers()
        {
            var users = _apm.GetUsers();
            return Ok(users);
        }

        /// <summary>
        ///     Creates a task on a work order
        /// </summary>
        /// <param name="newTask"></param>
        /// <returns></returns>
        [HttpPost("Task")]
        [Authorize(Policy = "APM - Edit")]
        public async Task<IActionResult> PostTask(NewTask newTask)
        {
            var shouldForbid = await ShouldForbidUpdate();
            if (shouldForbid != null) return shouldForbid;
            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            var email = CurrentUserEmail?.ToLower();
            var user = await _userProfiles.GetAsync(email);
            if (user == null) return Forbid();
            var workOrder = await _workOrders.Get(newTask.WorkOrderID, newTask.ProjectID, email);

            if (workOrder == null) return NotFound($"Work Order ID is invalid: {newTask.WorkOrderID}");

            try
            {
                var task = await _workOrders.CreateTask(workOrder, newTask, CurrentUserEmail, user.SelectedBusinessUnit);

                return Ok(task);
            }
            catch (InvalidOperationException e)
            {
                return BadRequest(e.Message);
            }
        }

        /// <summary>
        ///     Updates the header information of a work order's task
        /// </summary>
        /// <param name="id"></param>
        /// <param name="taskUpdate"></param>
        /// <returns></returns>
        [HttpPut("TaskHeader/{id}")]
        [Authorize(Policy = "APM - Edit")]
        public async Task<IActionResult> UpdateWorkOrderHeaderTask(string id, TaskUpdate taskUpdate)
        {
            var shouldForbid = await ShouldForbidUpdate();
            if (shouldForbid != null) return shouldForbid;

            var email = CurrentUserEmail?.ToLower();
            var workOrder = await _workOrders.Get(taskUpdate.WorkOrderID, taskUpdate.ProjectID, email);

            if (workOrder == null) return NotFound("Work Order ID is invalid");
            try
            {
                var task = await _workOrders.UpdateTask(workOrder, id, taskUpdate, CurrentUserEmail);
                return Ok(task);
            }
            catch (TasksNotFoundException e)
            {
                _logger.LogWarning(e.Message);
                return NotFound();
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(ex.Message);
            }
        }

        /// <summary>
        ///     Updates an existing contact on a project
        /// </summary>
        /// <param name="projectId"></param>
        /// <param name="contact"></param>
        /// <returns></returns>
        [HttpPut("Client/{projectId}")]
        [Authorize(Policy = "APM - Edit")]
        public async Task<IActionResult> PutContact(string projectId, ClientContact contact)
        {
            var email = CurrentUserEmail?.ToLower();
            var shouldForbid = await ShouldForbidUpdate();
            if (shouldForbid != null) return shouldForbid;
            var project = _projects.Get(projectId, email);

            if (project == null) return NotFound();

            try
            {
                var projectContact = await _projects.UpdateContact(project, contact, CurrentUserEmail);
                return Ok(projectContact);
            }
            catch (ProjectContactNotFoundException e)
            {
                _logger.LogWarning(e.Message);
                return NotFound();
            }
        }

        /// <summary>
        ///     Creates a contact on a project
        /// </summary>
        /// <param name="projectId"></param>
        /// <param name="contact"></param>
        /// <returns></returns>
        [HttpPost("Client/{projectId}")]
        [Authorize(Policy = "APM - Edit")]
        public async Task<IActionResult> PostContact(string projectId, ClientContact contact)
        {
            var email = CurrentUserEmail?.ToLower();
            var shouldForbid = await ShouldForbidUpdate();
            if (shouldForbid != null) return shouldForbid;
            var project = _projects.Get(projectId, email);
            if (project == null) return NotFound();

            var projectContact = await _projects.CreateContact(project, contact, CurrentUserEmail);

            return Ok(projectContact);
        }

        /// <summary>
        ///     Deletes a contact on a project
        /// </summary>
        /// <param name="projectId"></param>
        /// <param name="contactId"></param>
        /// <returns></returns>
        [HttpDelete("Client/{projectId}/{contactId}")]
        [Authorize(Policy = "APM - Edit")]
        public async Task<IActionResult> DeleteContact(string projectId, string contactId)
        {
            var email = CurrentUserEmail?.ToLower();
            var shouldForbid = await ShouldForbidUpdate();
            if (shouldForbid != null) return shouldForbid;
            var project = _projects.Get(projectId, email);

            if (project == null) return NotFound();

            await _projects.RemoveContact(project, contactId, CurrentUserEmail);

            return Ok();
        }

        /// <summary>
        ///     Updates the published state of a work order
        /// </summary>
        /// <param name="update"></param>
        /// <returns></returns>
        [HttpPut("PublishUnpublishWorkOrder")]
        [Authorize(Policy = "APM - ChangePublishedState")]
        public async Task<IActionResult> PublishUnpublishWorkOrder(PublishUpdateObject update)
        {
            var shouldForbid = await ShouldForbidUpdate();
            if (shouldForbid != null) return shouldForbid;
            var email = CurrentUserEmail?.ToLower();
            var workOrder = await _workOrders.Get(update.WorkOrderId, update.ProjectId, email);

            if (workOrder == null) return NotFound();

            workOrder = await _workOrders.UpdatePublishedState(workOrder, update, CurrentUserEmail);

            return Ok(workOrder);
        }

        /// <summary>
        ///     Updates a task on a work order
        /// </summary>
        /// <param name="id"></param>
        /// <param name="taskUpdate"></param>
        /// <returns></returns>
        [HttpPut("Task/{id}")]
        [Authorize(Policy = "APM - Edit")]
        public async Task<IActionResult> PutTask(string id, TaskUpdate taskUpdate)
        {
            var shouldForbid = await ShouldForbidUpdate();
            if (shouldForbid != null) return shouldForbid;

            var email = CurrentUserEmail?.ToLower();
            var workOrder = await _workOrders.Get(taskUpdate.WorkOrderID, taskUpdate.ProjectID, email);

            if (workOrder == null) return NotFound($"Work Order ID is invalid: ${taskUpdate.WorkOrderID}");

            var task = await _workOrders.UpdateTask(workOrder, id, taskUpdate, CurrentUserEmail);

            return Ok(task);
        }

        /// <summary>
        ///     Uploads an inspection file.  This is stored in One Insight's blob storage, not the APM blob storage
        ///     with the rest of the photos in both the mobile and web application.
        /// </summary>
        /// <param name="workOrderId"></param>
        /// <returns></returns>
        [HttpPost("InspectionFiles/{workOrderId}")]
        [Authorize(Policy = "APM - Edit")]
        public async Task<IActionResult> UploadInspectionFile(string workOrderId)
        {
            var shouldForbid = await ShouldForbidUpdate();
            if (shouldForbid != null) return shouldForbid;
            var file = Request.Form.Files[0];

            // Azure Blob Storage Migration
            //await _oneInsightStorage.UploadAttachmentAsync(workOrderId, file, CurrentUserEmail);
            await _oneInsightStorage.UploadAttachmentObjectAsync(workOrderId, file);
            return Ok();
        }

        /// <summary>
        ///     Gets a list of the inspection files for a work order
        /// </summary>
        /// <param name="workOrderId"></param>
        /// <returns></returns>
        [HttpGet("InspectionFiles/{workOrderId}")]
        [Authorize(Policy = "APM - View")]
        public async Task<IActionResult> GetInspectionFiles(string workOrderId)
        {
            // Azure Blob Storage Migration
            //var blobs = await _oneInsightStorage.ListBlobsAsync(workOrderId);
            var blobs = await _oneInsightStorage.ListObjectAsync(workOrderId);
            return Ok(blobs);
        }

        /// <summary>
        ///     Downloads a work order's inspection file
        /// </summary>
        /// <param name="workOrderId"></param>
        /// <param name="fileName"></param>
        /// <returns></returns>
        [HttpGet("InspectionFiles/{workOrderId}/{fileName}")]
        [Authorize(Policy = "APM - View")]
        public async Task<IActionResult> DownloadFile(string workOrderId, string fileName)
        {
            //Azure Blob Storage Migration
            //var file = await _oneInsightStorage.DownloadBlobAsync(workOrderId, fileName);
            var file = await _oneInsightStorage.DownloadObjectAsync(workOrderId, fileName);
            return File(file.Stream, file.ContentType, fileName);
        }

        /// <summary>
        ///     Deletes a work order's inspection file
        /// </summary>
        /// <param name="workOrderId"></param>
        /// <param name="fileName"></param>
        /// <returns></returns>
        [HttpDelete("InspectionFiles/{workOrderId}/{fileName}")]
        [Authorize(Policy = "APM - Edit")]
        public async Task<IActionResult> DeleteFile(string workOrderId, string fileName)
        {
            var shouldForbid = await ShouldForbidUpdate();
            if (shouldForbid != null) return shouldForbid;

            // Azure Blob Storage Migration
            //await _oneInsightStorage.DeleteBlobAsync(workOrderId, fileName);
            await _oneInsightStorage.DeleteObjectAsync(workOrderId, fileName);
            return Ok();
        }

        // <summary>
        //     Generates an APM report for a task on a work order.
        // </summary>
        // <param name="reportTransport"></param>
        // <returns></returns>
        [HttpPost("JasperSoft")]
        [Authorize(Policy = "APM - View")]
        public async Task<IActionResult> GetReport(ReportTransport reportTransport)
        {
            var email = CurrentUserEmail?.ToLower();
            if (reportTransport.ReportType == "Not Implemented")
                return new ObjectResult(reportTransport.AssetCategory + " " + reportTransport.TaskType +
                                        " Report is not currently supported")
                { StatusCode = 501 };

            // Get Work Order 
            var workOrder = await _workOrders.Get(reportTransport.WorkOrderId, reportTransport.ProjectId, email);
            if (workOrder == null) return NotFound();

            // Get Project 
            var project = _projects.Get(reportTransport.ProjectId, email);

            // Get Location 
            var location = await _locations.Get(project.locationId, email);

            try
            {
                var response =
                    await _jasperSoft.GenerateInspectionReport(reportTransport, project, location, workOrder);

                return Ok(response);

                //// Send file if successful response from JasperSoft 
                //if (response.IsSuccessStatusCode)
                //{
                //    var stream = await response.Content.ReadAsStreamAsync();
                //    return File(stream, "application/pdf");
                //}

                //// Return response from JasperSoft if not successful 
                //return StatusCode((int)response.StatusCode, response.Content);
            }
            catch (TasksNotFoundException e)
            {
                _logger.LogWarning($"Unable to find task(s) for report: {string.Join(", ", e.TaskIDs)}");
                return NotFound();
            }
        }

        ///// <summary>
        /////     Generates an APM report for a task on a work order.
        ///// </summary>
        ///// <param name="reportTransport"></param>
        ///// <returns></returns>
        [HttpPost("ReportObject")]
        [Authorize(Policy = "APM - View")]
        public async Task<IActionResult> GetReportObject(ReportTransport reportTransport)
        {
            var email = CurrentUserEmail?.ToLower();
            if (reportTransport.ReportType == "Not Implemented")
                return new ObjectResult(reportTransport.AssetCategory + " " + reportTransport.TaskType +
                                        " Report is not currently supported")
                { StatusCode = 501 };

            // Get Work Order 
            var workOrder = await _workOrders.Get(reportTransport.WorkOrderId, reportTransport.ProjectId, email);
            if (workOrder == null) return NotFound();

            // Get Project 
            var project = _projects.Get(reportTransport.ProjectId, email);

            // Get Location 
            var location = await _locations.Get(project.locationId, email);

            try
            {
                var (assetWalkDown, fullInspection) =
                    await _jasperSoft.GenerateInspectionReportObject(reportTransport, project, location, workOrder);

                return Ok(new { assetWalkDown, fullInspection });
            }
            catch (TasksNotFoundException e)
            {
                _logger.LogWarning($"Unable to find task(s) for report: {string.Join(", ", e.TaskIDs)}");
                return NotFound();
            }
            catch (Exception e)
            {
                _logger.LogError(e.Message);
                return NotFound();
            }
        }

        /// <summary>
        ///     Updates a walk down on a work order associated with a Piping asset (570)
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPut("FiveSeventyWalkDown")]
        [Authorize(Policy = "APM - Edit")]
        public async Task<IActionResult> UpdateFiveSeventyWalkDown(FiveSeventyWalkdown data)
        {
            var shouldForbid = await ShouldForbidUpdate();
            if (shouldForbid != null) return shouldForbid;
            var email = CurrentUserEmail?.ToLower();
            var workOrder = await _workOrders.Get(data.WorkOrderId, data.ProjectId, email);

            if (workOrder == null) return NotFound();

            await _workOrders.Update(workOrder, data, CurrentUserEmail);

            return Ok();
        }

        /// <summary>
        ///     Updates a walk down on a work order associated with a Tank asset (653)
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPut("SixFiftyThreeWalkDown")]
        [Authorize(Policy = "APM - Edit")]
        public async Task<IActionResult> UpdateSixFiftyThreeWalkDown(SixFiftyThreeWalkDown data)
        {
            var shouldForbid = await ShouldForbidUpdate();
            if (shouldForbid != null) return shouldForbid;
            var email = CurrentUserEmail?.ToLower();
            var workOrder = await _workOrders.Get(data.WorkOrderId, data.ProjectId, email);

            if (workOrder == null) return NotFound();

            await _workOrders.Update(workOrder, data, CurrentUserEmail);

            return Ok();
        }

        /// <summary>
        ///     Updates the photo description of a photo on the asset details (current walk down) of a work order
        /// </summary>
        /// <param name="photo"></param>
        /// <returns></returns>
        [HttpPut("AssetDetailsPhotos")]
        [Authorize(Policy = "APM - Edit")]
        public async Task<IActionResult> UpdateAssetDetailsPhotoDescription(AssetDetailsPhotoTransport photo)
        {
            var shouldForbid = await ShouldForbidUpdate();
            if (shouldForbid != null) return shouldForbid;
            try
            {
                await _workOrders.UpdateAssetDetailsPhotoDescription(photo, CurrentUserEmail);
            }
            catch (Exception e)
            {
                if (!(e is AssetDetailsPhotoNotFoundException) && !(e is TasksNotFoundException)) throw;
                _logger.LogWarning(e.Message);
                return NotFound();
            }

            return NoContent();
        }

        /// <summary>
        ///     Deletes a photo on the asset details (current walk down) of a work order
        /// </summary>
        /// <param name="photo"></param>
        /// <returns></returns>
        [HttpDelete("AssetDetailsPhotos")]
        [Authorize(Policy = "APM - Edit")]
        public async Task<IActionResult> DeleteAssetDetailsPhoto(AssetDetailsPhotoTransport photo)
        {
            var shouldForbid = await ShouldForbidUpdate();
            if (shouldForbid != null) return shouldForbid;
            try
            {
                await _workOrders.DeleteAssetDetailsPhoto(photo, CurrentUserEmail);
            }
            catch (Exception e)
            {
                if (!(e is AssetDetailsPhotoNotFoundException) && !(e is TasksNotFoundException)) throw;
                _logger.LogWarning(e.Message);
                return NotFound();
            }

            return Ok();
        }

        /// <summary>
        ///     Get a task by ID
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("Task/{id}")]
        [Authorize(Policy = "APM - View")]
        public async Task<IActionResult> GetTask(string id)
        {
            var email = CurrentUserEmail?.ToLower();
            var workOrders = await _workOrders.Get(email);
            var tasks = workOrders.SelectMany(w => w.tasks);
            var task = tasks.FirstOrDefault(t => t.id == id);

            if (task == null) return NotFound();

            return Ok(task);
        }

        /// <summary>
        ///     Query tasks with <see cref="DataSourceLoadOptions"/>
        /// </summary>
        /// <param name="options"></param>
        /// <returns></returns>
        [HttpGet("Tasks")]
        [Authorize(Policy = "APM - View")]
        public async Task<IActionResult> GetTasks(DataSourceLoadOptions options)
        {
            var email = CurrentUserEmail?.ToLower();
            var user = await _userProfiles.GetAsync(email);
            var workOrders = await _workOrders.Get(email);
            var tasks = workOrders
                .SelectMany(w => w.tasks.Where(task => task.businessUnitId.CurrentValue == user.SelectedBusinessUnit))
                .Select(t => _tasks.BuildTaskVM(t, email))
                .AsQueryable();
            var result = DataSourceLoader.Load(tasks, options);
            return Ok(result);
        }

        /// <summary>
        ///     Gets tasks associated to a particular project.  Will return tasks for all projects
        ///     the user has access to if no projectIds are provided.
        /// </summary>
        /// <param name="projectIds"></param>
        /// <returns></returns>
        [HttpGet("ProjectTasks")]
        [Authorize(Policy = "APM - View")]
        public async Task<IActionResult> GetTasksByProjectIds([FromQuery(Name = "projectIds")] string[] projectIds)
        {
            var email = CurrentUserEmail?.ToLower();
            var workOrders = projectIds.Length > 0
                ? await _apm.GetWorkOrders(projectIds, email)
                : await _workOrders.Get(email);
            var tasks = workOrders
                .SelectMany(w => w.tasks)
                .Select(t => _tasks.BuildTaskVM(t, email));
            return Ok(tasks);
        }

        /// <summary>
        ///     Query Work Orders with <see cref="DataSourceLoadOptions"/>
        /// </summary>
        /// <param name="options"></param>
        /// <returns></returns>
        [HttpGet("WorkOrdersVM")]
        [Authorize(Policy = "APM - View")]
        public async Task<IActionResult> GetWorkOrdersVM(DataSourceLoadOptions options)
        {
            var email = CurrentUserEmail?.ToLower();
            var user = await _userProfiles.GetAsync(email);
            var workOrders = await _workOrders.Get(email);
            var workOrderVMs = workOrders
                .Where(wo => wo.businessUnitId.CurrentValue == user.SelectedBusinessUnit)
                .Select(w => _workOrders.BuildWorkOrderVM(w))
                .AsQueryable();
            var result = DataSourceLoader.Load(workOrderVMs, options);
            return Ok(result);
        }

        /// <summary>
        ///    Generates a SAS token to access APM photos from the web api
        /// </summary>
        /// <returns></returns>
        [HttpGet("BlobSas")]
        [Authorize(Policy = "APM - View")]
        public async Task<IActionResult> GetBlobAuthToken()
        {
            // Azure Blob Storage Migration
            //return Ok(_oneInsightStorage.GetSasToken());
            return Ok(await _oneInsightStorage.GetSignedUrl());
        }

        [HttpGet("BlobSasObject/{objectName}")]
        [Authorize(Policy = "APM - View")]
        public async Task<IActionResult> GetBlobObjectAuthToken(string objectName)
        {
            return Ok(await _oneInsightStorage.GetSignedUrl(objectName));
        }


        /// <summary>
        ///     Get the email from the current request's user identity.
        /// </summary>
        public string CurrentUserEmail => User?.Identity?.Name?.ToLower();

        /// <summary>
        ///     Get clients
        /// </summary>
        /// <returns></returns>
        [HttpGet("Clients")]
        [Authorize(Policy = "APM - ManageClients")]
        public IActionResult GetClients()
        {
            return Ok(_apm.GetClients());
        }

        /// <summary>
        ///     Get business units
        /// </summary>
        /// <returns></returns>
        [HttpGet("BusinessUnits")]
        [Authorize(Policy = "APM - ManageClients")]
        public IActionResult GetBusinessUnits()
        {
            var businessUnits = _apm.GetBusinessUnits().OrderBy(bu => bu.Name.CurrentValue);
            return Ok(businessUnits);
        }

        /// <summary>
        ///     Return the business units that belong to the current user
        /// </summary>
        /// <returns></returns>
        [HttpGet("MyBusinessUnits")]
        [Authorize(Policy = "APM - View")]
        public IActionResult GetMyBusinessUnits()
        {
            var users = _apm.GetUsers();
            var me = users.SingleOrDefault(u =>
                new[] { u.id.Replace('|', '.').ToLower(), u.Email.ToLower() }.Contains(CurrentUserEmail));
            if (me == null) return NotFound();
            var businessUnits = _apm.GetBusinessUnits(me.BusinessUnitIds.CurrentValue).OrderBy(bu => bu.Name.CurrentValue);
            return Ok(businessUnits);
        }

        /// <summary>
        ///     Create a new client
        /// </summary>
        /// <param name="newClient"></param>
        /// <returns></returns>
        [HttpPost("Clients")]
        [Authorize(Policy = "APM - ManageClients")]
        public async Task<IActionResult> CreateClient(NewAPMClient newClient)
        {
            var client = new Client();
            client.Name.SetValue(newClient.Name);
            await client.SavePendingChanges(CurrentUserEmail);

            return Ok(client);
        }

        /// <summary>
        ///     Update a client.
        /// </summary>
        /// <param name="id"></param>
        /// <param name="update"></param>
        /// <returns></returns>
        [HttpPut("Clients/{id}")]
        [Authorize(Policy = "APM - ManageClients")]
        public async Task<IActionResult> UpdateClient(string id, APMClientUpdate update)
        {
            var client = GetClient(id);

            if (client == null) return NotFound();

            if (update.Name == null) return Ok();

            client.Name.SetValue(update.Name);
            await client.SavePendingChanges(CurrentUserEmail);

            return Ok();
        }

        /// <summary>
        ///     Create a business unit.
        /// </summary>
        /// <param name="newBusinessUnit"></param>
        /// <returns></returns>
        [HttpPost("BusinessUnits")]
        [Authorize(Policy = "APM - ManageClients")]
        public async Task<IActionResult> CreateBusinessUnit(NewBusinessUnit newBusinessUnit)
        {
            var client = GetClient(newBusinessUnit.ClientId);

            if (client == null) return NotFound();

            var businessUnit = await client.CreateBusinessUnit(newBusinessUnit.Name, CurrentUserEmail);

            var allUsers = _apm.GetUsers();

            foreach (var userId in newBusinessUnit.UserIds)
            {
                var user = allUsers.SingleOrDefault(u => u.id == userId);
                if (user == null) continue;
                user.BusinessUnitIds.AddValue(businessUnit.id);
                await user.SavePendingChanges(CurrentUserEmail);
            }

            return Ok(businessUnit);
        }

        /// <summary>
        ///     Update a business unit.
        /// </summary>
        /// <param name="id"></param>
        /// <param name="update"></param>
        /// <returns></returns>
        [HttpPut("BusinessUnits/{id}")]
        [Authorize(Policy = "APM - ManageClients")]
        public async Task<IActionResult> UpdateBusinessUnit(string id, BusinessUnitUpdate update)
        {
            var businessUnit = GetBusinessUnit(id);

            if (businessUnit == null) return NotFound();

            var allUsers = _apm.GetUsers();

            // Determines if a user has the business unit assigned to them.
            static bool HasBusinessUnit(APMWebDataInterface.DataModel.UserProfile user, string businessUnitId) =>
                user.BusinessUnitIds.CurrentValue.Contains(businessUnitId);

            // Determines if the update payload indicates the user SHOULD have the business unit assigned to them.
            bool ShouldHaveBusinessUnit(APMWebDataInterface.DataModel.UserProfile user) =>
                update.UserIds.Contains(user.id);

            // Find out which users need to be updated.
            var usersToRemove = allUsers
                .Where(u => HasBusinessUnit(u, id) && ShouldHaveBusinessUnit(u) == false);
            var usersToAdd = allUsers
                .Where(u => HasBusinessUnit(u, id) == false && ShouldHaveBusinessUnit(u));

            // Update the users who need access removed
            foreach (var user in usersToRemove)
            {
                user.BusinessUnitIds.RemoveValue(id);
                await user.SavePendingChanges(CurrentUserEmail);
            }

            // Update the users who need access granted
            foreach (var user in usersToAdd)
            {
                user.BusinessUnitIds.AddValue(id);
                await user.SavePendingChanges(CurrentUserEmail);
            }

            if (update.Name == null) return Ok();

            // Update the business unit.
            businessUnit.Name.SetValue(update.Name);
            await businessUnit.SavePendingChanges(CurrentUserEmail);

            return Ok();
        }

        private Client GetClient(string id)
        {
            var clients = _apm.GetClients(new[] { id });
            var client = clients.FirstOrDefault();
            return client;
        }

        private BusinessUnit GetBusinessUnit(string businessUnitId)
        {
            var businessUnits = _apm.GetBusinessUnits(new[] { businessUnitId });
            var businessUnit = businessUnits.FirstOrDefault();
            return businessUnit;
        }

        private async Task<IActionResult> ShouldForbidUpdate()
        {
            var email = CurrentUserEmail?.ToLower();
            var user = await _userProfiles.GetAsync(email);
            if (user == null)
            {
                return Unauthorized();
            }
            return ShouldForbidUpdate(user) ? Forbid() : null;
        }
    }
}