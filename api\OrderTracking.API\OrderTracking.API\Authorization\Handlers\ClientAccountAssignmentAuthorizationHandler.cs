using System;
using System.Linq;
using System.Threading.Tasks;
using ClientPortal.Shared.Extensions;
using ClientPortal.Shared.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.DependencyInjection;
using OrderTracking.API.Authorization.Requirements;
using OrderTracking.API.Services;

namespace OrderTracking.API.Authorization.Handlers
{
    /// <summary>
    ///     Handles authorization around providing access to client account numbers for User Profiles
    /// </summary>
    public class
        ClientAccountAssignmentAuthorizationHandler : AuthorizationHandler<ClientAccountAssignmentRequirement,
            UserProfile>
    {
        private readonly IUserProfilesService _serviceProvider;

        /// <summary>
        ///     Constructor
        /// </summary>
        /// <param name="serviceProvider"></param>
        public ClientAccountAssignmentAuthorizationHandler(IUserProfilesService serviceProvider)
        {
            _serviceProvider = serviceProvider;
        }

        /// <summary>
        ///     Handle the requirement.
        /// </summary>
        /// <param name="context"></param>
        /// <param name="requirement"></param>
        /// <param name="resource"></param>
        /// <returns></returns>
        protected override async Task HandleRequirementAsync(
            AuthorizationHandlerContext context,
            ClientAccountAssignmentRequirement requirement,
            UserProfile resource
        )
        {
            if (context == null) throw new ArgumentNullException(nameof(context));
            if (resource == null) throw new ArgumentNullException(nameof(resource));

            if (context.User.Identity?.Name != null)
                await AuthorizeChangesToClientAccounts(context, requirement, resource);
            else
                context.Fail();
        }

        private async Task AuthorizeChangesToClientAccounts(AuthorizationHandlerContext context,
            IAuthorizationRequirement requirement, UserProfile resource)
        {
            var clientAccountsChanged = await DetermineIfClientAccountsChanged(resource);

            // Get current user profile
           
            var assigner = await _serviceProvider.GetAsync(context.User.Identity?.Name);

            if (clientAccountsChanged && (assigner.HasRole("App:Admin") ||
                                          assigner.HasRole("WMO:DistrictManager")))
                context.Succeed(requirement);
            else if (!clientAccountsChanged)
                context.Succeed(requirement);
            else context.Fail();
        }

        private async Task<bool> DetermineIfClientAccountsChanged(UserProfile resource)
        {
            // Get user profile before change
            
            var originalUserProfile = await _serviceProvider.GetAsync(resource.Id);

            // See if client accounts given to user profile is changing
            var newClientAccounts = resource.CustomerAccounts.Except(originalUserProfile.CustomerAccounts);
            var removedClientAccounts = originalUserProfile.CustomerAccounts.Except(resource.CustomerAccounts);
            var clientAccountsChanged = newClientAccounts.Any() || removedClientAccounts.Any();
            return clientAccountsChanged;
        }
    }
}