﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.DependencyInjection;
using OrderTracking.API.Authorization.Requirements;
using OrderTracking.API.Services;

namespace OrderTracking.API.Authorization.Handlers
{
    /// <summary>
    ///     Handler for the <see cref="UserIsModuleAdminRequirement" />
    /// </summary>
    public class UserIsModuleAdminHandler : AuthorizationHandler<UserIsModuleAdminRequirement>
    {
        #region Fields and Constants

        private readonly IUserProfilesService _serviceProvider;

        #endregion Fields and Constants

        #region Constructors

        /// <summary>
        ///     Constructs the handler, injecting a service provider
        /// </summary>
        /// <param name="serviceProvider"></param>
        public UserIsModuleAdminHandler(IUserProfilesService serviceProvider)
        {
            _serviceProvider = serviceProvider;
        }

        #endregion Constructors

        /// <summary>
        ///     Handles the <see cref="UserIsModuleAdminRequirement"/>, setting the context to Success if the user is a module admin and Fail if user is not a module admin
        /// </summary>
        /// <param name="context"></param>
        /// <param name="requirement"></param>
        /// <returns></returns>
        protected override async Task HandleRequirementAsync(AuthorizationHandlerContext context,
            UserIsModuleAdminRequirement requirement)
        {
            if (context == null) throw new ArgumentNullException(nameof(context));

            if (!context.User.Identity.IsAuthenticated)
            {
                context.Fail();
                return;
            }

            var email = context.User.Identity.Name?.ToLower();
            if (email == null)
            {
                context.Fail();
                return;
            }

            
            var user = await _serviceProvider.GetAsync(email);
            if (user == null)
            {
                context.Fail();
                return;
            }

            if(user.Roles.Any(role => role.ToLower().Contains(":admin") ))
                context.Succeed((requirement));

        }
    }
}
