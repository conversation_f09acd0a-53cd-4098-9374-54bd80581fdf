# Fix-PackageVersions.ps1
# This script fixes package version conflicts and removes problematic packages

Write-Host "=== Fixing Package Version Conflicts ===" -ForegroundColor Green

# Set consistent package versions
$CosmosVersion = "3.35.4"
$IdentityClientVersion = "4.56.0"
$IdentityWebVersion = "1.25.0"

# Project paths
$OrderTrackingAPI = "api/OrderTracking.API/OrderTracking.API/OrderTracking.API.csproj"
$AuthorizationWeb = "api/TeamDigital.Authorization.Web/TeamDigital.Authorization.Web/TeamDigital.Authorization.Web.csproj"
$ClientPortalShared = "api/OrderTracking.API/ClientPortal.Shared/ClientPortal.Shared.csproj"

Write-Host "1. Removing DevExpress.Document.Processor from TeamDigital.Authorization.Web..." -ForegroundColor Yellow
dotnet remove $AuthorizationWeb package DevExpress.Document.Processor

Write-Host "2. Updating Microsoft.Azure.Cosmos to version $CosmosVersion..." -ForegroundColor Yellow
dotnet add $OrderTrackingAPI package Microsoft.Azure.Cosmos --version $CosmosVersion
dotnet add $AuthorizationWeb package Microsoft.Azure.Cosmos --version $CosmosVersion
dotnet add $ClientPortalShared package Microsoft.Azure.Cosmos --version $CosmosVersion

Write-Host "3. Updating Microsoft.Identity.Client to version $IdentityClientVersion..." -ForegroundColor Yellow
dotnet add $OrderTrackingAPI package Microsoft.Identity.Client --version $IdentityClientVersion
dotnet add $AuthorizationWeb package Microsoft.Identity.Client --version $IdentityClientVersion

Write-Host "4. Updating Microsoft.Identity.Web to version $IdentityWebVersion..." -ForegroundColor Yellow
dotnet add $OrderTrackingAPI package Microsoft.Identity.Web --version $IdentityWebVersion
dotnet add $AuthorizationWeb package Microsoft.Identity.Web --version $IdentityWebVersion

Write-Host "5. Cleaning previous builds..." -ForegroundColor Yellow
dotnet clean $OrderTrackingAPI
dotnet clean $AuthorizationWeb
dotnet clean $ClientPortalShared

Write-Host "6. Restoring packages..." -ForegroundColor Yellow
dotnet restore $OrderTrackingAPI --verbosity normal
dotnet restore $AuthorizationWeb --verbosity normal
dotnet restore $ClientPortalShared --verbosity normal

Write-Host "=== Package Version Fix Complete ===" -ForegroundColor Green
