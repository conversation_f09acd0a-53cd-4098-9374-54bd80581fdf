using Microsoft.ApplicationInsights;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;

namespace OrderTracking.API.Filters
{
    /// <summary>
    /// Action filter that logs API requests and responses to Application Insights
    /// </summary>
    public class ApiLoggingActionFilter : ActionFilterAttribute
    {
        private readonly ILogger<ApiLoggingActionFilter> _logger;
        private readonly TelemetryClient _telemetryClient;
        private Stopwatch _stopwatch;

        public ApiLoggingActionFilter(ILogger<ApiLoggingActionFilter> logger, TelemetryClient telemetryClient)
        {
            _logger = logger;
            _telemetryClient = telemetryClient;
        }

        public override void OnActionExecuting(ActionExecutingContext context)
        {
            _stopwatch = Stopwatch.StartNew();

            var controllerName = context.Controller.GetType().Name;
            var actionName = context.ActionDescriptor.DisplayName;
            var userId = context.HttpContext.User?.Identity?.Name ?? "Anonymous";
            var requestPath = context.HttpContext.Request.Path;
            var requestMethod = context.HttpContext.Request.Method;

            // Log request start
            _logger.LogInformation(
                "API Request Started: {ControllerName}.{ActionName} - Path: {RequestPath}, Method: {RequestMethod}, User: {UserId}",
                controllerName,
                actionName,
                requestPath,
                requestMethod,
                userId);

            // Track custom event in Application Insights
            var properties = new Dictionary<string, string>
            {
                { "ControllerName", controllerName },
                { "ActionName", actionName },
                { "RequestPath", requestPath.ToString() },
                { "RequestMethod", requestMethod },
                { "UserId", userId },
                { "UserAgent", context.HttpContext.Request.Headers["User-Agent"].ToString() },
                { "RemoteIP", context.HttpContext.Connection.RemoteIpAddress?.ToString() }
            };

            if (context.ActionArguments.Any())
            {
                properties.Add("ArgumentCount", context.ActionArguments.Count.ToString());
            }

            _telemetryClient.TrackEvent("API_Request_Started", properties);

            base.OnActionExecuting(context);
        }

        public override void OnActionExecuted(ActionExecutedContext context)
        {
            _stopwatch?.Stop();
            var executionTime = _stopwatch?.ElapsedMilliseconds ?? 0;

            var controllerName = context.Controller.GetType().Name;
            var actionName = context.ActionDescriptor.DisplayName;
            var userId = context.HttpContext.User?.Identity?.Name ?? "Anonymous";
            var requestPath = context.HttpContext.Request.Path;
            var requestMethod = context.HttpContext.Request.Method;
            var statusCode = context.HttpContext.Response.StatusCode;

            // Log request completion
            if (context.Exception != null)
            {
                _logger.LogError(context.Exception,
                    "API Request Failed: {ControllerName}.{ActionName} - Path: {RequestPath}, Method: {RequestMethod}, User: {UserId}, Duration: {ExecutionTime}ms, StatusCode: {StatusCode}",
                    controllerName,
                    actionName,
                    requestPath,
                    requestMethod,
                    userId,
                    executionTime,
                    statusCode);
            }
            else
            {
                _logger.LogInformation(
                    "API Request Completed: {ControllerName}.{ActionName} - Path: {RequestPath}, Method: {RequestMethod}, User: {UserId}, Duration: {ExecutionTime}ms, StatusCode: {StatusCode}",
                    controllerName,
                    actionName,
                    requestPath,
                    requestMethod,
                    userId,
                    executionTime,
                    statusCode);
            }

            // Track custom event in Application Insights
            var properties = new Dictionary<string, string>
            {
                { "ControllerName", controllerName },
                { "ActionName", actionName },
                { "RequestPath", requestPath.ToString() },
                { "RequestMethod", requestMethod },
                { "UserId", userId },
                { "StatusCode", statusCode.ToString() },
                { "Success", (context.Exception == null).ToString() },
                { "UserAgent", context.HttpContext.Request.Headers["User-Agent"].ToString() },
                { "RemoteIP", context.HttpContext.Connection.RemoteIpAddress?.ToString() }
            };

            var metrics = new Dictionary<string, double>
            {
                { "ExecutionTimeMs", executionTime }
            };

            _telemetryClient.TrackEvent("API_Request_Completed", properties, metrics);

            // Track execution time as dependency
            _telemetryClient.TrackDependency("API", $"{controllerName}.{actionName}", requestPath, DateTime.UtcNow.AddMilliseconds(-executionTime), TimeSpan.FromMilliseconds(executionTime), context.Exception == null);

            base.OnActionExecuted(context);
        }
    }
}
