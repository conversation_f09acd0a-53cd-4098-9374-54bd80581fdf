using Microsoft.ApplicationInsights;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Net;
using System.Threading.Tasks;

namespace OrderTracking.API.Middleware
{
    /// <summary>
    /// Global exception handling middleware that logs errors to Application Insights
    /// </summary>
    public class GlobalExceptionMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILogger<GlobalExceptionMiddleware> _logger;
        private readonly TelemetryClient _telemetryClient;

        public GlobalExceptionMiddleware(RequestDelegate next, ILogger<GlobalExceptionMiddleware> logger, TelemetryClient telemetryClient)
        {
            _next = next;
            _logger = logger;
            _telemetryClient = telemetryClient;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            try
            {
                await _next(context);
            }
            catch (Exception ex)
            {
                await HandleExceptionAsync(context, ex);
            }
        }

        private async Task HandleExceptionAsync(HttpContext context, Exception exception)
        {
            // Log to Application Insights
            var properties = new Dictionary<string, string>
            {
                { "RequestPath", context.Request.Path },
                { "RequestMethod", context.Request.Method },
                { "UserId", context.User?.Identity?.Name ?? "Anonymous" },
                { "UserAgent", context.Request.Headers["User-Agent"].ToString() },
                { "RemoteIP", context.Connection.RemoteIpAddress?.ToString() }
            };

            _telemetryClient.TrackException(exception, properties);

            // Log using structured logging
            _logger.LogError(exception, 
                "Unhandled exception occurred. Path: {RequestPath}, Method: {RequestMethod}, User: {UserId}",
                context.Request.Path,
                context.Request.Method,
                context.User?.Identity?.Name ?? "Anonymous");

            // Set response
            context.Response.StatusCode = (int)HttpStatusCode.InternalServerError;
            context.Response.ContentType = "application/json";

            var response = new
            {
                error = new
                {
                    message = "An internal server error occurred.",
                    statusCode = context.Response.StatusCode,
                    timestamp = DateTime.UtcNow,
                    traceId = context.TraceIdentifier
                }
            };

            var jsonResponse = JsonConvert.SerializeObject(response);
            await context.Response.WriteAsync(jsonResponse);
        }
    }
}
