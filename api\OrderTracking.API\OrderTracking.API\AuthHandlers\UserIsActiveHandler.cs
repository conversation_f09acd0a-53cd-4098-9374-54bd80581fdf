﻿using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.DependencyInjection;
using OrderTracking.API.Requirements;
using OrderTracking.API.Services;

namespace OrderTracking.API.AuthHandlers
{
    /// <summary>
    ///     Handles requests made to endpoints that require the <see cref="UserIsActiveRequirement"/>
    /// </summary>
    public class UserIsActiveHandler : AuthorizationHandler<UserIsActiveRequirement>
    {
        #region Fields and Constants

        private readonly IUserProfilesService _serviceProvider;

        #endregion Fields and Constants

        #region Constructors

        /// <summary>
        ///     Constructs the handler, injecting service provider to resolve scoped services
        /// </summary>
        /// <param name="serviceProvider"></param>
        public UserIsActiveHandler(IUserProfilesService serviceProvider)
        {
            _serviceProvider = serviceProvider;
        }

        #endregion Constructors

        /// <summary>
        ///     Handles the <see cref="UserIsActiveRequirement"/>, setting the context to Success if the user is active and Fail if user is not active
        /// </summary>
        /// <param name="context"></param>
        /// <param name="requirement"></param>
        /// <returns></returns>
        protected override async Task HandleRequirementAsync(AuthorizationHandlerContext context,
            UserIsActiveRequirement requirement)
        {
            if (context == null) throw new ArgumentNullException(nameof(context));

            if (!context.User.Identity.IsAuthenticated)
            {
                context.Fail();
                return;
            }

            //Cannot Test This.  Cannot Set email to null because ClaimsPrinciple params cannot accept null values
            var email = context.User.Identity.Name?.ToLower();
            if (email == null)
            {
                context.Fail();
                return;
            }

            
            
            var user = await _serviceProvider.GetAsync(email);
            if (user == null)
            {
                context.Fail();
                return;
            }

            if (user.Active) context.Succeed(requirement);
        }
    }
}