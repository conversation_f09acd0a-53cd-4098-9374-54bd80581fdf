using Microsoft.ApplicationInsights;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;

namespace OrderTracking.API.Services
{
    /// <summary>
    /// Service for structured logging with Application Insights integration
    /// </summary>
    public interface IApplicationLoggingService
    {
        void LogInformation(string message, object data = null);
        void LogWarning(string message, object data = null);
        void LogError(Exception exception, string message, object data = null);
        void LogError(string message, object data = null);
        void TrackEvent(string eventName, Dictionary<string, string> properties = null, Dictionary<string, double> metrics = null);
        void TrackDependency(string dependencyType, string dependencyName, string data, DateTime startTime, TimeSpan duration, bool success);
        void TrackUserAction(string userId, string action, string resource, bool success, object additionalData = null);
    }

    public class ApplicationLoggingService : IApplicationLoggingService
    {
        private readonly ILogger<ApplicationLoggingService> _logger;
        private readonly TelemetryClient _telemetryClient;

        public ApplicationLoggingService(ILogger<ApplicationLoggingService> logger, TelemetryClient telemetryClient)
        {
            _logger = logger;
            _telemetryClient = telemetryClient;
        }

        public void LogInformation(string message, object data = null)
        {
            _logger.LogInformation(message);
            
            if (data != null)
            {
                var properties = ConvertToProperties(data);
                _telemetryClient.TrackEvent("Information", properties);
            }
        }

        public void LogWarning(string message, object data = null)
        {
            _logger.LogWarning(message);
            
            if (data != null)
            {
                var properties = ConvertToProperties(data);
                _telemetryClient.TrackEvent("Warning", properties);
            }
        }

        public void LogError(Exception exception, string message, object data = null)
        {
            _logger.LogError(exception, message);
            
            var properties = data != null ? ConvertToProperties(data) : new Dictionary<string, string>();
            properties.Add("Message", message);
            
            _telemetryClient.TrackException(exception, properties);
        }

        public void LogError(string message, object data = null)
        {
            _logger.LogError(message);
            
            var properties = data != null ? ConvertToProperties(data) : new Dictionary<string, string>();
            properties.Add("Message", message);
            
            _telemetryClient.TrackEvent("Error", properties);
        }

        public void TrackEvent(string eventName, Dictionary<string, string> properties = null, Dictionary<string, double> metrics = null)
        {
            _telemetryClient.TrackEvent(eventName, properties, metrics);
        }

        public void TrackDependency(string dependencyType, string dependencyName, string data, DateTime startTime, TimeSpan duration, bool success)
        {
            _telemetryClient.TrackDependency(dependencyType, dependencyName, data, startTime, duration, success);
        }

        public void TrackUserAction(string userId, string action, string resource, bool success, object additionalData = null)
        {
            var properties = new Dictionary<string, string>
            {
                { "UserId", userId },
                { "Action", action },
                { "Resource", resource },
                { "Success", success.ToString() },
                { "Timestamp", DateTime.UtcNow.ToString("O") }
            };

            if (additionalData != null)
            {
                var additionalProperties = ConvertToProperties(additionalData);
                foreach (var prop in additionalProperties)
                {
                    properties.TryAdd($"Additional_{prop.Key}", prop.Value);
                }
            }

            _telemetryClient.TrackEvent("UserAction", properties);

            _logger.LogInformation(
                "User Action: {UserId} performed {Action} on {Resource} - Success: {Success}",
                userId, action, resource, success);
        }

        private Dictionary<string, string> ConvertToProperties(object data)
        {
            var properties = new Dictionary<string, string>();
            
            if (data == null) return properties;

            var type = data.GetType();
            var props = type.GetProperties();

            foreach (var prop in props)
            {
                try
                {
                    var value = prop.GetValue(data);
                    properties.Add(prop.Name, value?.ToString() ?? "null");
                }
                catch (Exception ex)
                {
                    properties.Add(prop.Name, $"Error reading property: {ex.Message}");
                }
            }

            return properties;
        }
    }
}
