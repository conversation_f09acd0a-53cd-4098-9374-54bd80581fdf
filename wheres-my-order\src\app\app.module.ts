import { HashLocationStrategy, LocationStrategy } from '@angular/common';
import { HTTP_INTERCEPTORS } from '@angular/common/http';
import { ApplicationRef, DoBootstrap, NgModule } from '@angular/core';
// Migrated from Firebase to Azure services
// import { initializeApp, provideFirebaseApp } from '@angular/fire/app';
// import { getFirestore, provideFirestore } from '@angular/fire/firestore';
import { FormsModule } from '@angular/forms';
import { BrowserModule } from '@angular/platform-browser';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { MsalRedirectComponent } from '@azure/msal-angular';
import { PowerBIEmbedModule } from 'powerbi-client-angular';
import { environment } from '../environments/environment';
import { DEFAULT_TIMEOUT, TimeoutInterceptor } from './aimaas/pages';
import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';
import { ConnectedWorkerComponent } from './connected-worker/connected-worker.component';
import { CoreModule } from './core/core.module';
import { MsalComponent } from './msal/msal.component';
import {
    AboutComponent,
    HelpAndFeedbackComponent,
    HomeComponent,
    RequestAccessComponent,
    VerificationCallbackComponent,
    VerificationComponent
} from './pages';
import { SharedModule } from './shared/shared.module';
//import { AssetGeneralinformationComponent } from './src/app/aimaas/components/asset-generalinformation/asset-generalinformation.component';

@NgModule({
    imports: [
        BrowserModule,
        BrowserAnimationsModule,
        FormsModule,
        CoreModule,
        SharedModule,
        AppRoutingModule,
        PowerBIEmbedModule,
        // Migrated from Firebase to Azure services
        // provideFirebaseApp(() => initializeApp(environment.firebase)),
        // provideFirestore(() => getFirestore())
    ],
    declarations: [
        AppComponent,
        HomeComponent,
        HelpAndFeedbackComponent,
        VerificationComponent,
        VerificationCallbackComponent,
        AboutComponent,
        MsalComponent,
        RequestAccessComponent,
        ConnectedWorkerComponent,
        // AssetGeneralinformationComponent
    ],
    providers: [
        { provide: LocationStrategy, useClass: HashLocationStrategy },
        [
            {
                provide: HTTP_INTERCEPTORS,
                useClass: TimeoutInterceptor,
                multi: true
            }
        ],
        [{ provide: DEFAULT_TIMEOUT, useValue: 500000 }]
    ],
    bootstrap: [AppComponent, MsalRedirectComponent]
})
export class AppModule implements DoBootstrap {
    ngDoBootstrap(ref: ApplicationRef) {
        if (window !== window.parent && !window.opener) {
            ref.bootstrap(MsalComponent);
        } else {
            ref.bootstrap(AppComponent);
        }
    }
}
